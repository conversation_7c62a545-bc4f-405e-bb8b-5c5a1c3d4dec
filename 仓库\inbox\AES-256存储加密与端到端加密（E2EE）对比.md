---
title: AES-256存储加密与端到端加密（E2EE）对比
source: "[[终极对比：AES-256存储加密 vs 端到端加密（E2EE）- AES-256（OneDrive）：      银行帮你保管金条，他们有备用钥匙（微软能解锁你的文件）。    - E2EE（Obsidian Sync）：      金条锁在你家的保险箱，钥匙只有你有。]]"
tags:
  - AES-256
  - E2EE
  - 对比
  - 特性
  - 应用
  - 安全性
keywords:
  - AES-256
  - E2EE
  - 存储加密
  - 端到端加密
  - 安全性
  - 应用
created: 2025-08-02
type: 原子笔记
---

# AES-256存储加密与端到端加密（E2EE）对比

| 特性 | **AES-256（存储+传输）** | **端到端加密（E2EE）** |
|---|---|---|
| **谁控制密钥？** | 服务商（如微软/百度） | **只有用户** |
| **谁能解密数据？** | 服务商+用户 | **仅用户** |
| **典型应用** | OneDrive/百度网盘 | Obsidian官方同步/Signal/ProtonMail |
| **安全性** | 防外部黑客，但服务商可看数据 | **防所有人（包括服务商）** |

---

## 元信息
- **来源笔记**: [[终极对比：AES-256存储加密 vs 端到端加密（E2EE）- AES-256（OneDrive）：      银行帮你保管金条，他们有备用钥匙（微软能解锁你的文件）。    - E2EE（Obsidian Sync）：      金条锁在你家的保险箱，钥匙只有你有。]]
- **创建时间**: 2025/8/2 20:56:46
- **标签**: #AES-256 #E2EE #对比 #特性 #应用 #安全性
- **关键词**: AES-256, E2EE, 存储加密, 端到端加密, 安全性, 应用

## 相关链接
- 返回原笔记: [[终极对比：AES-256存储加密 vs 端到端加密（E2EE）- AES-256（OneDrive）：      银行帮你保管金条，他们有备用钥匙（微软能解锁你的文件）。    - E2EE（Obsidian Sync）：      金条锁在你家的保险箱，钥匙只有你有。]]
