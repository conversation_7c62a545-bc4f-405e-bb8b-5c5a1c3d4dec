---
触发词:
  - AES加密
  - 高级加密标准
  - 对称加密
  - 数据加密算法
类型: 对称加密算法
技术层级: 算法层技术
实现难度: 中等复杂
成本投入: 中等成本
安全级别: 高级安全
应用场景:
  - 敏感字段加密
  - 文件加密
  - 网络通信
  - TDE实现
企业规模:
  - 中企业适用
  - 大企业标准
行业特征:
  - 金融标准
  - 医疗推荐
  - 政府认可
技术成熟度: 成熟技术
银行系统类比: 国际标准保险箱锁
合规顾问动作:
  - 验证AES实施规范
  - 检查密钥管理安全
  - 确认加密强度合规
技术特性:
  - 可逆加密
  - 高性能
  - 硬件加速支持
关联概念:
  - 透明数据加密TDE
  - 数据加密
  - 盐值Salt
合规相关:
  - FIPS140-2
  - GDPR认可
  - 等保推荐
已学: true
---

# AES加密

## 定义
AES（Advanced Encryption Standard）是一种对称加密算法，由美国国家标准与技术研究院（NIST）在2001年确立为加密标准，是目前最广泛使用的对称加密算法之一。

## 核心特征
- **对称加密**：加密和解密使用相同密钥
- **分组密码**：将数据分成固定大小的块进行加密
- **多种密钥长度**：支持128、192、256位密钥
- **高安全性**：目前被认为是安全的加密算法

## 技术规格
### 分组大小
- 固定128位（16字节）分组大小
- 不足128位的数据需要填充

### 密钥长度
- **AES-128**：128位密钥，10轮加密
- **AES-192**：192位密钥，12轮加密  
- **AES-256**：256位密钥，14轮加密

### 加密轮数
- 通过多轮替换和置换操作
- 轮数取决于密钥长度
- 每轮包括字节替换、行移位、列混合、轮密钥加

## 工作模式
### ECB模式（电子密码本）
- 每个分组独立加密
- 相同明文产生相同密文
- 不推荐使用，存在安全隐患

### CBC模式（密码分组链接）
- 使用初始化向量（IV）
- 前一个密文块影响后一个明文块
- 广泛使用的安全模式

### CTR模式（计数器）
- 将分组密码转换为流密码
- 支持并行加密
- 适合高性能场景

### GCM模式（伽罗瓦/计数器）
- 提供加密和认证
- 高性能且安全
- 现代应用的首选模式

## 应用场景
### 数据库字段加密
- 敏感字段如身份证号、银行卡号
- 配合[[数据加密]]策略使用
- 可逆加密，支持解密查看

### 文件加密
- 文档和文件的加密存储
- 磁盘加密
- 备份数据加密

### 网络通信
- HTTPS/TLS协议
- VPN连接
- 即时通讯加密

### [[透明数据加密(TDE)]]
- 数据库引擎层使用AES加密
- 自动加密存储文件
- 对应用透明

## 实际应用示例
### SQL数据库应用
```sql
-- MySQL AES加密
INSERT INTO users (name, phone) VALUES 
('张三', AES_ENCRYPT('13800138000', 'secret_key'));

-- 解密查询
SELECT name, AES_DECRYPT(phone, 'secret_key') as phone 
FROM users WHERE name = '张三';

-- Oracle AES加密
SELECT DBMS_CRYPTO.ENCRYPT(
    UTL_RAW.CAST_TO_RAW('13800138000'),
    DBMS_CRYPTO.AES_CBC_PKCS5,
    UTL_RAW.CAST_TO_RAW('secret_key_16bytes')
) FROM dual;
```

### 应用层加密
```python
from Crypto.Cipher import AES
from Crypto.Random import get_random_bytes
import base64

def aes_encrypt(plaintext, key):
    cipher = AES.new(key, AES.MODE_CBC)
    # 填充到16字节的倍数
    padded = plaintext + (16 - len(plaintext) % 16) * chr(16 - len(plaintext) % 16)
    ciphertext = cipher.encrypt(padded.encode())
    return base64.b64encode(cipher.iv + ciphertext).decode()

def aes_decrypt(ciphertext, key):
    data = base64.b64decode(ciphertext)
    iv = data[:16]
    encrypted = data[16:]
    cipher = AES.new(key, AES.MODE_CBC, iv)
    decrypted = cipher.decrypt(encrypted).decode()
    # 移除填充
    return decrypted[:-ord(decrypted[-1])]
```

## 优势
- **高安全性**：经过广泛验证，目前无有效攻击
- **高性能**：硬件和软件实现都很高效
- **标准化**：国际标准，广泛支持
- **灵活性**：多种密钥长度和工作模式

## 局限性
- **密钥管理**：密钥的安全存储和分发是挑战
- **性能开销**：相比明文存储有一定性能损失
- **实现复杂性**：正确实现需要专业知识
- **量子威胁**：理论上可被量子计算机破解

## 安全考虑
### 密钥管理
- 使用强随机数生成密钥
- 安全存储和传输密钥
- 定期轮换密钥
- 实施密钥访问控制

### 初始化向量（IV）
- 每次加密使用不同的IV
- IV不需要保密但必须唯一
- 随机生成IV

### 填充攻击防护
- 使用安全的填充方案
- 验证填充的正确性
- 防止填充预言攻击

## 与其他加密技术对比
| 特征 | AES | [[MD5算法]] | RSA |
|------|-----|-------------|-----|
| 类型 | 对称加密 | 哈希函数 | 非对称加密 |
| 可逆性 | 可逆 | 不可逆 | 可逆 |
| 性能 | 高 | 很高 | 低 |
| 密钥管理 | 复杂 | 无需密钥 | 更复杂 |
| 适用场景 | 数据加密 | 密码存储 | 密钥交换 |

## 相关概念
- [[数据加密]] - AES的主要应用领域
- [[透明数据加密(TDE)]] - 使用AES的数据库加密技术
- [[盐值(Salt)]] - 可与AES结合使用增强安全性
- [[访问控制]] - 配合使用的安全措施

## 最佳实践
1. **选择合适模式**：推荐使用GCM或CBC模式
2. **密钥长度**：至少使用AES-128，敏感数据使用AES-256
3. **IV管理**：确保IV的唯一性和随机性
4. **密钥轮换**：定期更换加密密钥
5. **安全实现**：使用经过验证的加密库
6. **性能优化**：在安全和性能间找到平衡

## 合规要求
- **FIPS 140-2**：美国联邦信息处理标准
- **Common Criteria**：国际信息安全评估标准
- **GDPR**：欧盟数据保护法规认可的加密方法
- **等保2.0**：中国网络安全等级保护推荐算法

## 发展趋势
- **硬件加速**：CPU内置AES指令集
- **量子抗性**：研究后量子密码学替代方案
- **云端应用**：云服务中的AES加密
- **物联网优化**：轻量级AES实现

## 企业应用建议
- **敏感数据加密** → 首选AES-256
- **高性能需求** → 使用硬件加速的AES
- **合规要求** → AES满足大多数合规标准
- **密钥管理** → 建立完善的密钥管理体系
