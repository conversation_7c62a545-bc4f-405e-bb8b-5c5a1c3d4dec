---
title: RESTful API简介
source: "[[API接口对接的常见方式1. RESTful API：   - 最常见的接口形式，通过HTTP请求(GETPOSTPUT等)来获取或修改数据   - 示例：用GET请求获取最新订单列表，用POST请求上传新的库存数量2. Webhooks：   - 一种反向API，由数据提供方主动推送数据到指定URL]]"
tags:
  - API接口
  - 数据传输
keywords:
  - RESTful API
  - HTTP请求
created: 2025-08-05
type: atomic-note
已学: true
---

# RESTful API简介

RESTful API是最常见的接口形式，它通过HTTP请求（如GET、POST、PUT等）来获取或修改数据。这种API广泛应用于各种网络服务中，因其简单、易用而受到广泛欢迎。
