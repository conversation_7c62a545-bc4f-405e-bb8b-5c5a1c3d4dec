# 待处理区

> [!quote] 📣 *Slogan*
>  自由体验人生
>  不当求做而不得的奴隶和做稳了的奴隶

> [!danger]- 📥 收集箱
> - 收集一切待办、思路、灵感。
> - 代码解析：not done 是指筛选没有完成的任务，Priority is lowest（优先级是最低的），Sort by due（按照截止日期进行排序），Limit 100（最多显示 100 个，防止内存爆了）
```tasks
not done
Priority is lowest
sort by due
limit 100
```
> [!success]- ✏️ 已存档
> - 用来记录项目的存档点，快速从中断的地方继续开始。
> - 代码解析：Not done 筛选出未完成的任务，tags includes 【标签】 ，Sort by due 按照截止日期进行排序，Limit 100（最多显示 100 个，防止内存爆了变卡）
```tasks
not done
tags includes #存档 
sort by due
limit 100
```

> [!warning]- ⚡️进行中
> 这是项目中正在进行的任务，相当于 GTD 中的下一步，让你永远清晰你的下一步。
> - not done（筛选出未完成任务）、Path includes things（文件路径为：things 文件夹）、Status. Name includes 进行中 （任务状态名为进行中。PS：任务状态可以自己在 tasks 插件中自己定义）、Limit 100（最多显示 100 个）
```tasks
not done
path includes 02-项目
status.name includes 进行中
limit 100
```
> [!tip]- 📆 最近 7 天
> - 截止日期为最近七天的（包括今天）
> - not done（筛选出未完成的任务）、Due before 6 days later（截止日期比 6 天之后的日期早）、Sort by due（根据截止日期排序）、Limit 100（最多显示 100 个，防止内存爆了变卡）
```tasks
not done
due before 6 days later
sort by due
limit 100
```
> [!summary]- 🗓 本月安排
> - due （截止日期）在本月的任务便会通过 tasks 插件的语法自动筛选到本视图中
> - 代码解析：Due this month（截止日期在本月）、Group by function task. Due. Format (xxxx) 按照此日期格式进行分组、Sort by function task. File. Filename （按照文件名字进行排序，这样一个文件里的任务就会挨着）
Limit 100
```tasks
not done
due this month
group by function task.due.format("YYYY[%%]-MM[%%] MMM ")
sort by function task.file.filename 
limit 100
```
> [!important]- 📆 每月安排
>
> 👇引用此页面，便可以创建🏆里程碑，在反向链接中可以查看今年的里程碑事件❗️
```tasks
not done
due this year
group by function task.due.format("YYYY[%%]-MM[%%] MMM ")
limit 100
```
> [!attention]- 👀 关注中
> - 最近在意的事件，比如催更的博主，吃瓜后续，股市熊市状况，活动赛事的进展……
> - not done 是指筛选没有完成的任务。Tags includes xx （标签包括关注）、Sort by due（按照截止日期进行排序）、Limit 100（最多显示 100 个，防止内存爆了变卡）
```tasks
not done
tags includes #关注 
sort by due
limit 100
```
