async function atomicNoteSplitterFixed() {
  try {
    // 获取当前活动文件
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
      new Notice("请先打开一个笔记文件");
      return;
    }

    const fileContent = await app.vault.cachedRead(activeFile);
    const title = activeFile.basename;
    
    // 检查文件内容
    if (!fileContent || fileContent.trim().length < 50) {
      new Notice("文件内容太少，无法进行有效分割");
      return;
    }

    // 创建弹窗界面
    class AtomicNoteSplitterModal extends obsidian.Modal {
      constructor(app) {
        super(app);
      }
      
      onOpen() {
        this.titleEl.setText("原子笔记分割器配置");
        this.setupUI();
      }
      
      onClose() {
        this.contentEl.empty();
      }
      
      setupUI() {
        const contentEl = this.contentEl;
        contentEl.empty();
        contentEl.style.padding = "20px";
        contentEl.style.minWidth = "500px";
        
        // 显示当前文件信息
        const fileInfoDiv = contentEl.createDiv();
        fileInfoDiv.style.marginBottom = "15px";
        fileInfoDiv.style.padding = "10px";
        fileInfoDiv.style.backgroundColor = "#f0f0f0";
        fileInfoDiv.style.borderRadius = "4px";
        fileInfoDiv.innerHTML = `<strong>当前文件:</strong> ${title}<br><strong>内容长度:</strong> ${fileContent.length} 字符`;
        
        // API提供商选择
        const providerDiv = contentEl.createDiv();
        providerDiv.createEl("label", { text: "API提供商：" });
        const providerSelect = providerDiv.createEl("select");
        providerSelect.style.width = "100%";
        providerSelect.style.marginBottom = "15px";
        providerSelect.createEl("option", { value: "zhipu", text: "智谱AI (GLM)" });
        providerSelect.createEl("option", { value: "deepseek", text: "DeepSeek" });
        
        // API密钥输入
        const apiKeyDiv = contentEl.createDiv();
        apiKeyDiv.createEl("label", { text: "API密钥：" });
        const apiKeyInput = apiKeyDiv.createEl("input", { 
          type: "password", 
          placeholder: "请输入API密钥" 
        });
        apiKeyInput.style.width = "100%";
        apiKeyInput.style.marginBottom = "15px";
        
        // 模型选择
        const modelDiv = contentEl.createDiv();
        modelDiv.createEl("label", { text: "模型：" });
        const modelSelect = modelDiv.createEl("select");
        modelSelect.style.width = "100%";
        modelSelect.style.marginBottom = "15px";
        
        // 根据提供商更新模型选项
        const updateModelOptions = () => {
          modelSelect.innerHTML = "";
          if (providerSelect.value === "zhipu") {
            modelSelect.createEl("option", { value: "GLM-4-Flash", text: "GLM-4-Flash (免费)" });
            modelSelect.createEl("option", { value: "glm-4-plus", text: "GLM-4-Plus (付费)" });
          } else if (providerSelect.value === "deepseek") {
            modelSelect.createEl("option", { value: "deepseek-chat", text: "DeepSeek Chat" });
            modelSelect.createEl("option", { value: "deepseek-coder", text: "DeepSeek Coder" });
          }
        };
        
        providerSelect.addEventListener("change", updateModelOptions);
        updateModelOptions();
        
        // 输出文件夹
        const folderDiv = contentEl.createDiv();
        folderDiv.createEl("label", { text: "输出文件夹：" });
        const folderInput = folderDiv.createEl("input", { 
          type: "text", 
          placeholder: "atomic-notes",
          value: "atomic-notes"
        });
        folderInput.style.width = "100%";
        folderInput.style.marginBottom = "20px";
        
        // 按钮容器
        const buttonDiv = contentEl.createDiv();
        buttonDiv.style.textAlign = "right";
        
        const cancelButton = buttonDiv.createEl("button", { text: "取消" });
        cancelButton.style.marginRight = "10px";
        
        const confirmButton = buttonDiv.createEl("button", { text: "开始分割" });
        confirmButton.style.backgroundColor = "#007acc";
        confirmButton.style.color = "white";
        confirmButton.style.border = "none";
        confirmButton.style.padding = "8px 16px";
        confirmButton.style.borderRadius = "4px";
        
        // 取消按钮事件
        cancelButton.addEventListener("click", () => {
          this.close();
        });
        
        // 确认按钮事件
        confirmButton.addEventListener("click", async () => {
          const provider = providerSelect.value;
          const apiKey = apiKeyInput.value.trim();
          const model = modelSelect.value;
          const outputFolder = folderInput.value.trim() || "atomic-notes";
          
          if (!apiKey) {
            new Notice("请输入API密钥");
            return;
          }
          
          this.close();
          
          // 执行分割
          await performAtomicSplitFixed(provider, apiKey, model, outputFolder, title, fileContent);
        });
      }
    }
    
    const modal = new AtomicNoteSplitterModal(app);
    modal.open();
    
  } catch (error) {
    new Notice("创建界面时出错：" + error.message);
    console.error("Modal creation error:", error);
  }
}

async function performAtomicSplitFixed(provider, apiKey, model, outputFolder, title, fileContent) {
  try {
    // 创建输出文件夹
    try {
      await app.vault.createFolder(outputFolder);
    } catch (e) {
      // 文件夹已存在，忽略错误
    }
    
    // 构建更强化的提示词
    const prompt = `请将以下笔记内容分割成原子笔记。你必须严格按照JSON格式返回，不要添加任何其他文字。

笔记标题：${title}
笔记内容：
${fileContent}

要求：
1. 每个原子笔记包含一个独立知识点
2. 内容长度100-500字
3. 生成简洁标题
4. 必须返回有效的JSON格式

返回格式（不要包含markdown代码块）：
{"atomic_notes":[{"title":"标题","content":"内容","tags":["标签"],"keywords":["关键词"]}]}`;
    
    // 根据提供商构建API请求
    let apiOptions;
    
    if (provider === "zhipu") {
      apiOptions = {
        method: "POST",
        url: "https://open.bigmodel.cn/api/paas/v4/chat/completions",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          model: model,
          messages: [
            {
              role: "system",
              content: "你是一个专业的知识管理助手。你必须严格按照用户要求的JSON格式返回结果，不要添加任何解释或说明文字。"
            },
            {
              role: "user",
              content: prompt,
            },
          ],
          temperature: 0.3,
        }),
      };
    } else if (provider === "deepseek") {
      apiOptions = {
        method: "POST",
        url: "https://api.deepseek.com/chat/completions",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          model: model,
          messages: [
            {
              role: "system",
              content: "你是一个专业的知识管理助手。你必须严格按照用户要求的JSON格式返回结果，不要添加任何解释或说明文字。"
            },
            {
              role: "user",
              content: prompt,
            },
          ],
          temperature: 0.3,
        }),
      };
    }
    
    new Notice("正在分析笔记内容...");
    console.log("发送API请求:", apiOptions);
    
    const response = await obsidian.requestUrl(apiOptions);
    const result = response.json;
    
    console.log("API响应:", result);
    
    if (!result.choices || result.choices.length === 0) {
      new Notice("AI分析失败 - 没有返回内容");
      console.error("API响应错误:", result);
      return;
    }

    const aiResponse = result.choices[0].message?.content;
    if (!aiResponse) {
      new Notice("AI分析失败 - 响应为空");
      console.error("AI响应为空:", result.choices[0]);
      return;
    }

    console.log("AI原始返回:", aiResponse);

    // 尝试解析JSON
    let atomicNotesData;
    try {
      // 多种方式清理响应
      let cleanResponse = aiResponse.trim();
      
      // 移除markdown代码块
      cleanResponse = cleanResponse.replace(/```json\s*/g, '').replace(/```\s*/g, '');
      
      // 查找JSON对象
      const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanResponse = jsonMatch[0];
      }
      
      console.log("清理后的JSON:", cleanResponse);
      atomicNotesData = JSON.parse(cleanResponse);
      
    } catch (parseError) {
      new Notice("AI返回格式错误，尝试手动创建原子笔记...");
      console.error("JSON解析错误:", parseError);
      
      // 如果JSON解析失败，创建一个简单的分割
      await createSimpleSplit(title, fileContent, outputFolder);
      return;
    }

    if (!atomicNotesData.atomic_notes || !Array.isArray(atomicNotesData.atomic_notes)) {
      new Notice("AI响应数据格式不正确，尝试手动分割...");
      await createSimpleSplit(title, fileContent, outputFolder);
      return;
    }

    const atomicNotes = atomicNotesData.atomic_notes;
    new Notice(`发现 ${atomicNotes.length} 个原子笔记，正在创建文件...`);

    // 创建每个原子笔记文件
    let createdCount = 0;
    for (let i = 0; i < atomicNotes.length; i++) {
      const note = atomicNotes[i];
      
      if (!note.title || !note.content) {
        continue;
      }

      // 生成安全的文件名
      const safeTitle = note.title
        .replace(/[<>:"/\\|?*\n\r\t]/g, '_')
        .replace(/[_\s]+/g, '_')
        .replace(/^_+|_+$/g, '')
        .substring(0, 50);
      
      const fileName = `${safeTitle}.md`;
      const filePath = `${outputFolder}/${fileName}`;
      
      // 生成原子笔记内容
      const currentTime = new Date().toISOString().split('T')[0];
      const tags = note.tags || [];
      const keywords = note.keywords || [];

      const noteContent = `---
title: "${note.title}"
source: "[[${title}]]"
tags: [${tags.map(tag => `"${tag}"`).join(', ')}]
keywords: [${keywords.map(keyword => `"${keyword}"`).join(', ')}]
created: ${currentTime}
type: atomic-note
provider: ${provider}
model: ${model}
---

# ${note.title}

${note.content}
`;

      try {
        // 检查文件是否已存在，如果存在则添加序号
        let finalPath = filePath;
        let counter = 1;
        while (await app.vault.adapter.exists(finalPath)) {
          const nameWithoutExt = safeTitle;
          finalPath = `${outputFolder}/${nameWithoutExt}_${counter}.md`;
          counter++;
        }
        
        await app.vault.create(finalPath, noteContent);
        createdCount++;
        
      } catch (createError) {
        console.error(`创建文件失败 ${filePath}:`, createError);
      }
    }

    new Notice(`✅ 成功创建了 ${createdCount} 个原子笔记！`);
    
  } catch (error) {
    new Notice("发生错误 - 请检查控制台");
    console.error("原子笔记分割错误:", error);
  }
}

// 简单分割备用方案
async function createSimpleSplit(title, fileContent, outputFolder) {
  try {
    // 按段落分割
    const paragraphs = fileContent.split('\n\n').filter(p => p.trim().length > 50);
    
    let createdCount = 0;
    for (let i = 0; i < paragraphs.length; i++) {
      const paragraph = paragraphs[i].trim();
      if (!paragraph) continue;
      
      const noteTitle = `${title}_片段_${i + 1}`;
      const fileName = `${noteTitle}.md`;
      const filePath = `${outputFolder}/${fileName}`;
      
      const currentTime = new Date().toISOString().split('T')[0];
      const noteContent = `---
title: "${noteTitle}"
source: "[[${title}]]"
created: ${currentTime}
type: atomic-note-simple
---

# ${noteTitle}

${paragraph}
`;

      try {
        let finalPath = filePath;
        let counter = 1;
        while (await app.vault.adapter.exists(finalPath)) {
          finalPath = `${outputFolder}/${noteTitle}_${counter}.md`;
          counter++;
        }
        
        await app.vault.create(finalPath, noteContent);
        createdCount++;
        
      } catch (createError) {
        console.error(`创建文件失败 ${filePath}:`, createError);
      }
    }
    
    new Notice(`✅ 使用简单分割方式创建了 ${createdCount} 个笔记片段！`);
    
  } catch (error) {
    new Notice("简单分割也失败了");
    console.error("简单分割错误:", error);
  }
}

exports.default = {
  entry: atomicNoteSplitterFixed,
  name: "atomicNoteSplitterFixed",
  description: `修复版原子笔记分割器 - 增强错误处理和备用方案

使用方法：
直接调用 \`atomicNoteSplitterFixed()\` 即可

特性：
- 🔧 增强的错误处理
- 🛡️ AI格式错误时的备用分割方案
- 📊 详细的调试信息
- 🎯 更精确的提示词
`,
};
