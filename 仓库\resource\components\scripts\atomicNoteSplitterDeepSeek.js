async function atomicNoteSplitterDeepSeek() {
  // 创建弹窗界面
  const modal = new obsidian.Modal(app);

  modal.titleEl.setText("DeepSeek 原子笔记分割器");

  // 创建表单元素
  const contentEl = modal.contentEl;
  contentEl.empty();
  contentEl.style.padding = "20px";
  contentEl.style.minWidth = "500px";

  // API密钥输入
  const apiKeyDiv = contentEl.createDiv();
  apiKeyDiv.createEl("label", { text: "DeepSeek API密钥：" });
  const apiKeyInput = apiKeyDiv.createEl("input", {
    type: "password",
    placeholder: "请输入DeepSeek API密钥"
  });
  apiKeyInput.style.width = "100%";
  apiKeyInput.style.marginBottom = "15px";

  // 模型选择
  const modelDiv = contentEl.createDiv();
  modelDiv.createEl("label", { text: "模型：" });
  const modelSelect = modelDiv.createEl("select");
  modelSelect.style.width = "100%";
  modelSelect.style.marginBottom = "15px";
  modelSelect.createEl("option", { value: "deepseek-chat", text: "deepseek-chat (推荐)" });
  modelSelect.createEl("option", { value: "deepseek-coder", text: "deepseek-coder" });

  // 输出文件夹
  const folderDiv = contentEl.createDiv();
  folderDiv.createEl("label", { text: "输出文件夹：" });
  const folderInput = folderDiv.createEl("input", {
    type: "text",
    placeholder: "atomic-notes",
    value: "atomic-notes"
  });
  folderInput.style.width = "100%";
  folderInput.style.marginBottom = "15px";

  // 自定义提示词
  const promptDiv = contentEl.createDiv();
  promptDiv.createEl("label", { text: "自定义提示词（可选）：" });
  const promptTextarea = promptDiv.createEl("textarea", {
    placeholder: `留空使用默认提示词，或输入自定义提示词...

默认提示词会：
1. 分析笔记内容并切分成独立的原子笔记
2. 每个原子笔记包含独立完整的知识点
3. 生成简洁明确的标题
4. 自动添加标签和关键词
5. 保持markdown格式`
  });
  promptTextarea.style.width = "100%";
  promptTextarea.style.height = "120px";
  promptTextarea.style.marginBottom = "20px";
  promptTextarea.style.resize = "vertical";

  // 按钮容器
  const buttonDiv = contentEl.createDiv();
  buttonDiv.style.textAlign = "right";

  const cancelButton = buttonDiv.createEl("button", { text: "取消" });
  cancelButton.style.marginRight = "10px";

  const confirmButton = buttonDiv.createEl("button", { text: "开始分割" });
  confirmButton.style.backgroundColor = "#007acc";
  confirmButton.style.color = "white";
  confirmButton.style.border = "none";
  confirmButton.style.padding = "8px 16px";
  confirmButton.style.borderRadius = "4px";

  // 取消按钮事件
  cancelButton.addEventListener("click", () => {
    modal.close();
  });

  // 确认按钮事件
  confirmButton.addEventListener("click", async () => {
    const apiKey = apiKeyInput.value.trim();
    const model = modelSelect.value;
    const outputFolder = folderInput.value.trim() || "atomic-notes";
    const customPrompt = promptTextarea.value.trim();

    if (!apiKey) {
      new Notice("请输入DeepSeek API密钥");
      return;
    }

    modal.close();

    // 执行分割
    await performDeepSeekSplit(apiKey, outputFolder, model, customPrompt);
  });

  modal.open();
}

async function performDeepSeekSplit(token, outputFolderName, modelType, customPrompt) {
  const model = modelType || "deepseek-chat";
  
  const { currentFile } = this;
  const file = currentFile;
  const fileContent = await app.vault.cachedRead(file);
  const title = file.basename;
  
  // 创建输出文件夹
  const outputFolder = outputFolderName || "atomic-notes";
  try {
    await app.vault.createFolder(outputFolder);
  } catch (e) {
    // 文件夹已存在，忽略错误
  }
  
  // 构建提示词 - 支持自定义
  const defaultPrompt = `
作为一个专业的知识管理专家，请分析以下笔记内容，将其切分成独立的原子笔记。

笔记标题：${title}

笔记内容：
${fileContent || ""}

请按照以下要求进行切分：
1. 每个原子笔记应该包含一个独立、完整的知识点
2. 原子笔记应该具有独立性，可以单独理解和使用
3. 每个原子笔记的内容长度应该适中（100-500字）
4. 保留原文的核心信息，不要遗漏重要内容
5. 为每个原子笔记生成一个简洁明确的标题

请以JSON格式返回结果，格式如下：
{
  "atomic_notes": [
    {
      "title": "原子笔记标题",
      "content": "原子笔记内容（保持markdown格式）",
      "tags": ["标签1", "标签2"],
      "keywords": ["关键词1", "关键词2"]
    }
  ]
}

只返回JSON格式的结果，不要包含其他说明文字。
`;

  // 如果有自定义提示词，则使用自定义的，否则使用默认的
  const analysisPrompt = customPrompt || defaultPrompt;

  // 调用DeepSeek API进行内容分析
  const analysisOptions = {
    method: "POST",
    url: "https://api.deepseek.com/chat/completions",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model: model,
      messages: [
        {
          role: "user",
          content: analysisPrompt,
        },
      ],
    }),
  };

    new Notice("Analyzing note content...");
  
  try {
    const response = await obsidian.requestUrl(analysisOptions);
    const result = response.json;
    
    if (result.choices.length === 0) {
      new Notice("AI analysis failed - no content returned");
      return;
    }

    const aiResponse = result.choices[0].message?.content;
    if (!aiResponse) {
      new Notice("AI analysis failed - empty response");
      return;
    }

    // 解析AI返回的JSON
    let atomicNotesData;
    try {
      // 清理可能的markdown代码块标记
      const cleanResponse = aiResponse.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      atomicNotesData = JSON.parse(cleanResponse);
    } catch (parseError) {
      new Notice("AI response format error - cannot parse JSON");
      console.error("JSON解析错误:", parseError);
      console.log("AI原始返回:", aiResponse);
      return;
    }

    if (!atomicNotesData.atomic_notes || !Array.isArray(atomicNotesData.atomic_notes)) {
      new Notice("AI response data format incorrect");
      return;
    }

    const atomicNotes = atomicNotesData.atomic_notes;
    new Notice(`Found ${atomicNotes.length} atomic notes, creating files...`);

    // 创建每个原子笔记文件
    let createdCount = 0;
    for (let i = 0; i < atomicNotes.length; i++) {
      const note = atomicNotes[i];
      
      if (!note.title || !note.content) {
        continue;
      }

      // 生成安全的文件名
      const safeTitle = note.title
        .replace(/[<>:"/\\|?*\n\r\t]/g, '_')
        .replace(/[_\s]+/g, '_')
        .replace(/^_+|_+$/g, '')
        .substring(0, 50);
      
      const fileName = `${safeTitle}.md`;
      const filePath = `${outputFolder}/${fileName}`;
      
      // 生成原子笔记内容
      const currentTime = new Date().toISOString().split('T')[0];
      const tags = note.tags || [];
      const keywords = note.keywords || [];

      // 处理标签和关键词的格式
      const tagsStr = tags.length > 0 ? `[${tags.map(tag => `"${tag}"`).join(', ')}]` : '[]';
      const keywordsStr = keywords.length > 0 ? `[${keywords.map(keyword => `"${keyword}"`).join(', ')}]` : '[]';

      const noteContent = `---
title: "${note.title}"
source: "[[${title}]]"
tags: ${tagsStr}
keywords: ${keywordsStr}
created: ${currentTime}
type: atomic-note
provider: deepseek
model: ${model}
---

# ${note.title}

${note.content}
`;

      try {
        // 检查文件是否已存在，如果存在则添加序号
        let finalPath = filePath;
        let counter = 1;
        while (await app.vault.adapter.exists(finalPath)) {
          const nameWithoutExt = safeTitle;
          finalPath = `${outputFolder}/${nameWithoutExt}_${counter}.md`;
          counter++;
        }
        
        await app.vault.create(finalPath, noteContent);
        createdCount++;
        
      } catch (createError) {
        console.error(`创建文件失败 ${filePath}:`, createError);
      }
    }

    // Skip index file creation as requested

    new Notice(`✅ Successfully created ${createdCount} atomic notes!`);
    
  } catch (error) {
    new Notice("Error occurred - please check console");
    console.error("Atomic note splitting error:", error);
  }
}

exports.default = {
  entry: atomicNoteSplitterDeepSeek,
  name: "atomicNoteSplitterDeepSeek",
  description: `DeepSeek 原子笔记分割器 - 带UI界面和自定义提示词

使用方法：
直接调用 \`atomicNoteSplitterDeepSeek()\` 即可

功能特性：
- 🎨 友好的弹窗配置界面
- 🤖 DeepSeek AI驱动的原子笔记分割
- ✏️ 支持自定义提示词
- 📝 保留核心内容
- 🏷️ 自动生成标签和关键词
- 📁 结构化输出
- 🔗 保持与源笔记的链接

支持的模型：
- deepseek-chat (推荐)
- deepseek-coder

自定义提示词功能：
- 可以完全自定义AI如何分析和切分笔记
- 留空则使用默认的专业提示词
- 支持针对特定类型内容的定制化分析

API密钥获取：https://platform.deepseek.com/
`,
};
