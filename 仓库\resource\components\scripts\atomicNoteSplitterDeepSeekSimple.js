async function atomicNoteSplitterDeepSeek(token, outputFolderName, modelType, customPrompt) {
  const model = modelType || "deepseek-chat"; // DeepSeek默认模型
  
  if (!outputFolderName || !token) {
    new Notice("Please set API key and output folder name");
    return;
  }
  
  const { currentFile } = this;
  const file = currentFile;
  const fileContent = await app.vault.cachedRead(file);
  const title = file.basename;
  
  // 创建输出文件夹
  const outputFolder = outputFolderName || "atomic-notes";
  try {
    await app.vault.createFolder(outputFolder);
  } catch (e) {
    // 文件夹已存在，忽略错误
  }
  
  // 默认AI提示语
  const defaultPrompt = `
作为一个专业的知识管理专家，请分析以下笔记内容，将其切分成独立的原子笔记。

笔记标题：${title}

笔记内容：
${fileContent || ""}

请按照以下要求进行切分：
1. 每个原子笔记应该包含一个独立、完整的知识点
2. 原子笔记应该具有独立性，可以单独理解和使用
3. 每个原子笔记的内容长度应该适中（100-500字）
4. 保留原文的核心信息，不要遗漏重要内容
5. 为每个原子笔记生成一个简洁明确的标题

请以JSON格式返回结果，格式如下：
{
  "atomic_notes": [
    {
      "title": "原子笔记标题",
      "content": "原子笔记内容（保持markdown格式）",
      "tags": ["标签1", "标签2"],
      "keywords": ["关键词1", "关键词2"]
    }
  ]
}

只返回JSON格式的结果，不要包含其他说明文字。
`;

  // 如果有自定义提示词，则使用自定义的，否则使用默认的
  const analysisPrompt = customPrompt || defaultPrompt;

  // 调用DeepSeek API进行内容分析
  const analysisOptions = {
    method: "POST",
    url: "https://api.deepseek.com/chat/completions",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model: model,
      messages: [
        {
          role: "user",
          content: analysisPrompt,
        },
      ],
    }),
  };

    new Notice("Analyzing note content...");
  
  try {
    const response = await obsidian.requestUrl(analysisOptions);
    const result = response.json;
    
    if (result.choices.length === 0) {
      new Notice("AI analysis failed - no content returned");
      return;
    }

    const aiResponse = result.choices[0].message?.content;
    if (!aiResponse) {
      new Notice("AI analysis failed - empty response");
      return;
    }

    // 解析AI返回的JSON
    let atomicNotesData;
    try {
      // 清理可能的markdown代码块标记
      const cleanResponse = aiResponse.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      atomicNotesData = JSON.parse(cleanResponse);
    } catch (parseError) {
      new Notice("AI response format error - cannot parse JSON");
      console.error("JSON解析错误:", parseError);
      console.log("AI原始返回:", aiResponse);
      return;
    }

    if (!atomicNotesData.atomic_notes || !Array.isArray(atomicNotesData.atomic_notes)) {
      new Notice("AI response data format incorrect");
      return;
    }

    const atomicNotes = atomicNotesData.atomic_notes;
    new Notice(`Found ${atomicNotes.length} atomic notes, creating files...`);

    // 创建每个原子笔记文件
    let createdCount = 0;
    for (let i = 0; i < atomicNotes.length; i++) {
      const note = atomicNotes[i];
      
      if (!note.title || !note.content) {
        continue;
      }

      // 生成安全的文件名
      const safeTitle = note.title
        .replace(/[<>:"/\\|?*\n\r\t]/g, '_')
        .replace(/[_\s]+/g, '_')
        .replace(/^_+|_+$/g, '')
        .substring(0, 50);
      
      const fileName = `${safeTitle}.md`;
      const filePath = `${outputFolder}/${fileName}`;
      
      // 生成原子笔记内容
      const currentTime = new Date().toISOString().split('T')[0];
      const tags = note.tags || [];
      const keywords = note.keywords || [];

      // 处理标签和关键词的格式
      const tagsStr = tags.length > 0 ? `[${tags.map(tag => `"${tag}"`).join(', ')}]` : '[]';
      const keywordsStr = keywords.length > 0 ? `[${keywords.map(keyword => `"${keyword}"`).join(', ')}]` : '[]';

      const noteContent = `---
title: "${note.title}"
source: "[[${title}]]"
tags: ${tagsStr}
keywords: ${keywordsStr}
created: ${currentTime}
type: atomic-note
provider: deepseek
model: ${model}
---

# ${note.title}

${note.content}
`;

      try {
        // 检查文件是否已存在，如果存在则添加序号
        let finalPath = filePath;
        let counter = 1;
        while (await app.vault.adapter.exists(finalPath)) {
          const nameWithoutExt = safeTitle;
          finalPath = `${outputFolder}/${nameWithoutExt}_${counter}.md`;
          counter++;
        }
        
        await app.vault.create(finalPath, noteContent);
        createdCount++;
        
      } catch (createError) {
        console.error(`创建文件失败 ${filePath}:`, createError);
      }
    }

    // Skip index file creation as requested

    new Notice(`✅ Successfully created ${createdCount} atomic notes!`);
    
  } catch (error) {
    new Notice("Error occurred - please check console");
    console.error("Atomic note splitting error:", error);
  }
}

exports.default = {
  entry: atomicNoteSplitterDeepSeek,
  name: "atomicNoteSplitterDeepSeek",
  description: `Split current note into atomic notes using DeepSeek API with custom prompt support

  ==Register at https://platform.deepseek.com/ to get API key.==

  Usage:

  **Basic usage:**
  \`atomicNoteSplitterDeepSeek('your_api_key', 'output_folder_name')\`

  **With specific model:**
  \`atomicNoteSplitterDeepSeek('your_api_key', 'output_folder_name', 'deepseek-chat')\`
  \`atomicNoteSplitterDeepSeek('your_api_key', 'output_folder_name', 'deepseek-coder')\`

  **With custom prompt:**
  \`atomicNoteSplitterDeepSeek('your_api_key', 'output_folder_name', 'deepseek-chat', 'your custom prompt here')\`

  Parameters:
  - token: DeepSeek API key
  - outputFolderName: Output folder name
  - modelType: Model name (optional, defaults to 'deepseek-chat')
  - customPrompt: Custom prompt for AI analysis (optional, uses default if not provided)

  Custom Prompt Examples:

  **For technical docs:**
  \`"请将以下技术文档分解成独立的知识点，每个知识点解释一个技术概念，包含代码示例和实际应用。"\`

  **For study notes:**
  \`"请将以下学习内容整理成复习卡片，每张卡片包含一个核心概念、定义、例子和应用场景。"\`

  **For meeting records:**
  \`"请将以下会议记录分解成行动项，提取关键决策和任务，明确责任人和时间节点。"\`

  Features:
  - 🤖 DeepSeek AI-powered atomic note splitting
  - ✏️ Custom prompt support for specialized analysis
  - 📝 Preserves core content
  - 🏷️ Auto-generates tags and keywords
  - 📁 Creates structured output
  - 🔗 Maintains links to source note
  `,
};
