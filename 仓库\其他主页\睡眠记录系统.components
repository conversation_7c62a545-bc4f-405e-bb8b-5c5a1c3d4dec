{"components": [{"id": "97869fe6-b85d-4a93-a286-b21935b69692", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2025-06-28T16:37:06.384Z", "updateAt": "2025-06-28T16:37:06.384Z", "components": [{"componentId": "3ef1d482-2caf-4e18-9cb4-027503f4ca81", "layout": {"mobile": {"x": 0, "y": 0, "w": 4, "h": 4}, "laptop": {"x": 5, "y": 0, "w": 7, "h": 28}}}, {"componentId": "174695fe-d652-41e4-b417-f326e98d2aa1", "layout": {"mobile": {"x": 0, "y": 4, "w": 4, "h": 4}, "laptop": {"x": 2, "y": 10, "w": 3, "h": 18}}}, {"componentId": "41309b29-1236-486c-962d-c3a5b548da07", "layout": {"mobile": {"x": 0, "y": 8, "w": 4, "h": 4}, "laptop": {"x": 0, "y": 10, "w": 2, "h": 18}}}, {"componentId": "6464e89f-7d28-484b-915a-f91df53ebcea", "layout": {"mobile": {"x": 0, "y": 12, "w": 4, "h": 4}, "laptop": {"x": 0, "y": 0, "w": 2, "h": 10}}}, {"componentId": "6f27b902-bead-45da-b7d5-ece864659467", "layout": {"mobile": {"x": 0, "y": 0, "w": 4, "h": 4}, "laptop": {"x": 2, "y": 0, "w": 3, "h": 10}}}], "layoutType": "grid", "locked": false, "layoutOptions": {}}, {"id": "3ef1d482-2caf-4e18-9cb4-027503f4ca81", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-02-15T07:07:28.004Z", "updateAt": "2025-02-15T07:07:28.004Z", "viewType": "table", "newPageNameFormat": "{{date:YYYYMMDDHHmmss}} ", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "文件名", "type": "text", "isShow": true, "wrap": false, "options": {"width": "97"}}, {"id": "48cf2025-20a3-4ee8-85c2-b9720d01a0e8", "name": "睡觉", "isShow": true, "type": "text", "options": {"formatType": "datetime", "width": "59", "format": ""}}, {"id": "ad673c17-8422-4d1d-a748-2fb3efb33448", "name": "起床", "isShow": true, "type": "text", "options": {"width": "52"}}, {"id": "38f11209-cd58-446c-b338-6983477c0465", "name": "睡眠时间", "isShow": true, "type": "formula", "options": {"formula": "calculateSleepTime()", "renderType": "html", "width": "114"}}, {"id": "15acbf8e-b5f7-4ed3-a40f-1ee2c09ec3f7", "name": "睡眠评价", "isShow": true, "type": "formula", "options": {"formula": "calculateSleepQuality()", "renderType": "markdown"}}, {"id": "e42cc0dc-f32b-48c1-8d6d-59348091ff3a", "name": "睡眠评价pro", "isShow": true, "type": "formula", "options": {"formula": "calculateSleepQualityPro()", "renderType": "markdown", "width": "416"}}], "templates": [], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "showGrid": false, "heightType": "auto", "heightValue": 600}, "filter": {"id": "7a088476-686a-49aa-b8d7-3c86ac286f57", "type": "group", "operator": "and", "conditions": [{"id": "55916216-83c6-4062-94de-6f683323589d", "type": "filter", "operator": "contains", "property": "${file.tags}", "value": "#Diary", "conditions": []}, {"id": "13ad4f33-9e30-450d-80b0-ac75c66c0a94", "type": "filter", "operator": "time_after_or_equal", "property": "Date", "value": {"type": "$relativeTime", "unit": "day", "direction": "before", "value": "15"}, "conditions": []}]}, "sort": {"orders": [{"id": "f06b7168-aa07-4f9e-bdf4-469e8c7e1f1b", "property": "${file.basename}", "direction": "desc", "disabled": false}]}}, {"id": "174695fe-d652-41e4-b417-f326e98d2aa1", "type": "markdown", "titleAlign": "center", "tabTitle": "作息", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2025-04-18T16:51:34.300Z", "updateAt": "2025-04-18T16:51:34.300Z", "maxHeight": 490, "contentAlign": "left", "markdownsSource": "content", "markdownValue": "```tracker\nsearchType: frontmatter\nsearchTarget: 起床, 睡觉\nfolder: 01 记录/1 日记\nstartDate: -15d\nendDate: 0d\nline:\n    title: \"睡眠时间\"\n    yAxisLabel: \"起床,睡觉\"\n    yAxisLocation: left, right\n    \n\tyMin: 00:00, 00:00\n    yMax: 24:00, 24:00\n\tyAxisColor: \"#ffa500, #c777c8\"\n    reverseYAxis: true\n\tlineColor: \"#ffa500, #c777c8\"\n\tpointColor: \"#ffa500, #c777c8\"\n    showPoint: true\n```\n\n\n"}, {"id": "41309b29-1236-486c-962d-c3a5b548da07", "type": "markdown", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2025-06-29T04:15:34.458Z", "updateAt": "2025-06-29T04:15:34.458Z", "maxHeight": 546, "contentAlign": "left", "markdownsSource": "content", "markdownValue": "```dataviewjs\n// 获取最近15天的日期范围\nconst endDate = new Date();\nconst startDate = new Date();\nstartDate.setDate(endDate.getDate() - 15); // 15天前\n\n// 获取所有带有 #diary 标签且日期在最近15天内的笔记\nconst pages = dv.pages('#diary')\n    .where(p => {\n        if (!p.Date) return false;\n        const noteDate = new Date(p.Date);\n        return noteDate >= startDate && noteDate <= endDate;\n    })\n    .sort(p => p.Date, 'asc'); // 按日期排序\n\n// 计算睡眠时间（小时），仅当睡觉时间在 00:00-04:00 或 20:00-24:00 时\nfunction calculateSleepHours(wakeTimeStr, sleepTimeStr) {\n    const [wakeHours, wakeMins] = wakeTimeStr.split(':').map(Number);\n    const [sleepHours, sleepMins] = sleepTimeStr.split(':').map(Number);\n    \n    const isValidSleepTime = \n        (sleepHours >= 0 && sleepHours <= 4) ||   // 00:00-04:00\n        (sleepHours >= 20 && sleepHours <= 23);   // 20:00-23:59\n    \n    if (!isValidSleepTime) return null;\n    \n    const isSameDay = (sleepHours >= 0 && sleepHours <= 4);\n    \n    let totalMinutes;\n    if (isSameDay) {\n        totalMinutes = (wakeHours - sleepHours) * 60 + (wakeMins - sleepMins);\n    } else {\n        totalMinutes = (24 - sleepHours + wakeHours) * 60 + (wakeMins - sleepMins);\n    }\n    \n    return totalMinutes / 60;\n}\n\n// 计算平均时间（正确处理跨天时间）\nfunction calculateCircularAverageTime(timeStrings) {\n    if (timeStrings.length === 0) return \"无数据\";\n    \n    let totalSin = 0;\n    let totalCos = 0;\n    \n    for (const timeStr of timeStrings) {\n        const [hours, mins] = timeStr.split(':').map(Number);\n        const minutes = hours * 60 + mins;\n        const angle = (2 * Math.PI * minutes) / (24 * 60);\n        totalSin += Math.sin(angle);\n        totalCos += Math.cos(angle);\n    }\n    \n    const avgAngle = Math.atan2(totalSin / timeStrings.length, totalCos / timeStrings.length);\n    let avgMinutes = (avgAngle * (24 * 60)) / (2 * Math.PI);\n    if (avgMinutes < 0) avgMinutes += 24 * 60;\n    \n    const hours = Math.floor(avgMinutes / 60) % 24;\n    const minutes = Math.round(avgMinutes % 60);\n    \n    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;\n}\n\n// 将时间字符串转换为分钟数以便比较\nfunction timeToMinutes(timeStr) {\n    const [hours, mins] = timeStr.split(':').map(Number);\n    return hours * 60 + mins;\n}\n\n// 收集数据\nconst sleepTimes = [];\nconst wakeTimes = [];\nlet totalSleepHours = 0;\nlet validEntries = 0;\n\n// 初始化极值变量 - 使用统一的时间偏移量进行比较\nconst REFERENCE_HOUR = 20; // 以20:00为基准点\nconst REFERENCE_MINUTES = REFERENCE_HOUR * 60;\n\nlet earliestSleep = { time: \"23:59\", date: \"\", offset: Infinity };\nlet latestSleep = { time: \"00:00\", date: \"\", offset: -Infinity };\nlet latestWake = { time: \"00:00\", date: \"\", minutes: 0 };\nlet earliestWake = { time: \"23:59\", date: \"\", minutes: 1439 };\n\nfor (const page of pages) {\n    if (page.起床 && page.睡觉) {\n        const sleepHours = calculateSleepHours(page.起床, page.睡觉);\n        if (sleepHours !== null) {\n            // 记录睡觉和起床时间\n            sleepTimes.push(page.睡觉);\n            wakeTimes.push(page.起床);\n            totalSleepHours += sleepHours;\n            validEntries++;\n            \n            // 计算当前时间的分钟数\n            const sleepMinutes = timeToMinutes(page.睡觉);\n            const wakeMinutes = timeToMinutes(page.起床);\n            const dateStr = page.Date ? new Date(page.Date).toLocaleDateString() : \"未知日期\";\n            \n            // 计算相对于参考点的时间偏移量\n            let sleepOffset = sleepMinutes - REFERENCE_MINUTES;\n            if (sleepOffset < 0) sleepOffset += 24 * 60; // 处理负值\n            \n            // 更新最早睡觉时间（偏移量最小）\n            if (sleepOffset < earliestSleep.offset) {\n                earliestSleep = {\n                    time: page.睡觉,\n                    date: dateStr,\n                    offset: sleepOffset\n                };\n            }\n            \n            // 更新最晚睡觉时间（偏移量最大）\n            if (sleepOffset > latestSleep.offset) {\n                latestSleep = {\n                    time: page.睡觉,\n                    date: dateStr,\n                    offset: sleepOffset\n                };\n            }\n            \n            // 更新最晚起床时间\n            if (wakeMinutes > latestWake.minutes) {\n                latestWake = {\n                    time: page.起床,\n                    date: dateStr,\n                    minutes: wakeMinutes\n                };\n            }\n            \n            // 更新最早起床时间\n            if (wakeMinutes < earliestWake.minutes) {\n                earliestWake = {\n                    time: page.起床,\n                    date: dateStr,\n                    minutes: wakeMinutes\n                };\n            }\n        }\n    }\n}\n\n// 显示结果（带颜色样式）\nif (validEntries > 0) {\n    dv.paragraph(`<span style=\"color:#c777c8; font-weight:bold\">💤最近15天睡眠统计</span>`);\n    dv.paragraph(`<span style=\"color:#FFC000; font-weight:bold\">平均睡眠时间</span>: ${(totalSleepHours / validEntries).toFixed(1)} 小时`);\n    dv.paragraph(`<span style=\"color:#FFC000; font-weight:bold\">平均睡觉时间</span>: ${calculateCircularAverageTime(sleepTimes)}`);\n    dv.paragraph(`<span style=\"color:#FFC000; font-weight:bold\">平均起床时间</span>: ${calculateCircularAverageTime(wakeTimes)}`);\n    \n    dv.paragraph(`<span style=\"color:#c777c8; font-weight:bold\">极值统计</span>`);\n    dv.paragraph(`<span style=\"color:#FFC000; font-weight:bold\">最早睡觉</span>: ${earliestSleep.time} (${earliestSleep.date})`);\n    dv.paragraph(`<span style=\"color:#FFC000; font-weight:bold\">最晚睡觉</span>: ${latestSleep.time} (${latestSleep.date})`);\n    dv.paragraph(`<span style=\"color:#FFC000; font-weight:bold\">最晚起床</span>: ${latestWake.time} (${latestWake.date})`);\n    dv.paragraph(`<span style=\"color:#FFC000; font-weight:bold\">最早起床</span>: ${earliestWake.time} (${earliestWake.date})`);\n    \n    dv.paragraph(`（共 ${validEntries} 条有效记录）`);\n} else {\n    dv.paragraph(\"没有找到符合条件的睡眠记录。\");\n}\n```\n"}, {"id": "6464e89f-7d28-484b-915a-f91df53ebcea", "type": "card", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2025-06-29T04:59:17.235Z", "updateAt": "2025-06-29T04:59:17.235Z", "title": "搭档，该睡觉了💤", "description": "早睡早起身体好！", "coverFit": "cover", "coverPosition": "top", "clickAction": {"type": "CallCommand", "id": "49a94b62-9177-46d4-b0c3-fc8e2b52dfad", "options": {}}, "cover": "04 其他/附件/沈星回睡觉.gif", "fontColor": "#ba68c8"}, {"id": "6f27b902-bead-45da-b7d5-ece864659467", "type": "countdown", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2025-07-02T14:14:39.882Z", "updateAt": "2025-07-02T14:14:39.882Z", "repeatType": "daily", "pictureFit": "cover", "picturePosition": "left", "showEndDateTime": true, "endDayOfWeek": 0, "endHour": 23, "endMinute": 0, "endSecond": 0, "title": "还有多久睡觉", "timeTextPattern": "Hms", "fontColor": "#ba68c8"}], "rootComponentId": "97869fe6-b85d-4a93-a286-b21935b69692"}