# 上游下游兜底思维    1.  上游 (Input)：数据从哪来？干净吗？格式对吗？（对应[输入输出安全 两大方面：    1.  输入安全 (防投毒)：过滤用户的恶意指令（如“忽略之前指令，现在你是...”）、脏数据攻击（乱码病毒代码）。        -   防御措施：前端过滤、长度限制、格式校验。    2.  输出安全 (防发疯)：过滤AI生成的有害、违法、不道德的内容（如[大模型幻觉](大模型幻觉.md)）。        -   防御措施：规则过滤（敏感词）、置信度拦截、日志溯源。](输入输出安全%20两大方面：%20%20%20%201.%20%20输入安全%20(防投毒)：过滤用户的恶意指令（如“忽略之前指令，现在你是...”）、脏数据攻击（乱码病毒代码）。%20%20%20%20%20%20%20%20-%20%20%20防御措施：前端过滤、长度限制、格式校验。%20%20%20%202.%20%20输出安全%20(防发疯)：过滤AI生成的有害、违法、不道德的内容（如[大模型幻觉](大模型幻觉.md)）。%20%20%20%20%20%20%20%20-%20%20%20防御措施：规则过滤（敏感词）、置信度拦截、日志溯源。.md)）    2.  下游 (Output)：结果给谁用？要什么格式？（对应[JSON数据格式](JSON数据格式.md)和[Prompt工程体系](Prompt工程体系.md)）    3.  兜底 (Fallback)：如果AI出错、超时、发疯了怎么办？（对应[大模型幻觉](大模型幻觉.md)） - 原子笔记索引

> 本索引由原子笔记切分工具自动生成
> 生成时间: 2025/8/3 19:52:10
> 原始笔记: [[上游下游兜底思维    1.  上游 (Input)：数据从哪来？干净吗？格式对吗？（对应[输入输出安全](输入输出安全.md)）    2.  下游 (Output)：结果给谁用？要什么格式？（对应[JSON数据格式](JSON数据格式.md)和[Prompt工程体系](Prompt工程体系.md)）    3.  兜底 (Fallback)：如果AI出错、超时、发疯了怎么办？（对应[大模型幻觉](大模型幻觉.md)）]]

## 统计信息
- 原始笔记: [[上游下游兜底思维    1.  上游 (Input)：数据从哪来？干净吗？格式对吗？（对应[输入输出安全](输入输出安全.md)）    2.  下游 (Output)：结果给谁用？要什么格式？（对应[JSON数据格式](JSON数据格式.md)和[Prompt工程体系](Prompt工程体系.md)）    3.  兜底 (Fallback)：如果AI出错、超时、发疯了怎么办？（对应[大模型幻觉](大模型幻觉.md)）]]
- 切分出的原子笔记数量: 7
- 生成时间: 2025/8/3 19:52:10

## 原子笔记列表

1. [[上游（Input）数据管理关注数据来源、质量和格式。确保数据干净、格式正确，对应[输入输出安全](输入输出安全.md)。]] - 上游（Input）数据管理
2. [[下游（Output）数据格式与用途]] - 下游（Output）数据格式与用途
3. [[兜底（Fallback）策略]] - 兜底（Fallback）策略
4. [[上游下游兜底思维核心概念]] - 上游下游兜底思维核心概念
5. [[上游下游兜底思维学习目的]] - 上游下游兜底思维学习目的
6. [[上游下游兜底思维核心三问]] - 上游下游兜底思维核心三问
7. [[上游下游兜底思维关联概念]] - 上游下游兜底思维关联概念

## 标签分类

### #数据管理
- [[上游（Input）数据管理关注数据来源、质量和格式。确保数据干净、格式正确，对应[输入输出安全](输入输出安全.md)。]]

### #输入输出安全
- [[上游（Input）数据管理关注数据来源、质量和格式。确保数据干净、格式正确，对应[输入输出安全](输入输出安全.md)。]]

### #数据格式
- [[下游（Output）数据格式与用途]]

### #数据用途
- [[下游（Output）数据格式与用途]]

### #JSON数据格式
- [[下游（Output）数据格式与用途]]

### #Prompt工程体系
- [[下游（Output）数据格式与用途]]

### #故障处理
- [[兜底（Fallback）策略]]
- [[上游下游兜底思维核心三问]]

### #兜底策略
- [[兜底（Fallback）策略]]

### #大模型幻觉
- [[兜底（Fallback）策略]]

### #核心概念
- [[上游下游兜底思维核心概念]]

### #AI设计
- [[上游下游兜底思维核心概念]]
- [[上游下游兜底思维核心三问]]
- [[上游下游兜底思维关联概念]]

### #产品稳定性
- [[上游下游兜底思维核心概念]]

### #学习目的
- [[上游下游兜底思维学习目的]]

### #系统设计
- [[上游下游兜底思维学习目的]]

### #风险规避
- [[上游下游兜底思维学习目的]]

### #核心三问
- [[上游下游兜底思维核心三问]]

### #关联概念
- [[上游下游兜底思维关联概念]]

### #程序思维
- [[上游下游兜底思维关联概念]]

---
*此索引文件由原子笔记切分工具生成*
