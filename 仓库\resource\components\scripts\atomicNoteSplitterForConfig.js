async function atomicNoteSplitterForConfig() {
  try {
    // 获取当前活动文件
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
      new Notice("请先打开一个笔记文件");
      return;
    }

    const fileContent = await app.vault.cachedRead(activeFile);
    const title = activeFile.basename;
    
    // 检查文件内容
    if (!fileContent || fileContent.trim().length < 50) {
      new Notice("文件内容太少，无法进行有效分割");
      return;
    }

    // 创建弹窗界面
    class AtomicNoteSplitterModal extends obsidian.Modal {
      constructor(app) {
        super(app);
      }
      
      onOpen() {
        this.titleEl.setText("原子笔记分割器 - 配置文件专用版");
        this.setupUI();
      }
      
      onClose() {
        this.contentEl.empty();
      }
      
      setupUI() {
        const contentEl = this.contentEl;
        contentEl.empty();
        contentEl.style.padding = "20px";
        contentEl.style.minWidth = "500px";
        
        // 显示当前文件信息
        const fileInfoDiv = contentEl.createDiv();
        fileInfoDiv.style.marginBottom = "15px";
        fileInfoDiv.style.padding = "10px";
        fileInfoDiv.style.backgroundColor = "#f0f0f0";
        fileInfoDiv.style.borderRadius = "4px";
        fileInfoDiv.innerHTML = `<strong>当前文件:</strong> ${title}<br><strong>内容长度:</strong> ${fileContent.length} 字符<br><strong>文件类型:</strong> 配置文件/JSON数据`;
        
        // API提供商选择
        const providerDiv = contentEl.createDiv();
        providerDiv.createEl("label", { text: "API提供商：" });
        const providerSelect = providerDiv.createEl("select");
        providerSelect.style.width = "100%";
        providerSelect.style.marginBottom = "15px";
        providerSelect.createEl("option", { value: "zhipu", text: "智谱AI (GLM)" });
        providerSelect.createEl("option", { value: "deepseek", text: "DeepSeek" });
        
        // API密钥输入
        const apiKeyDiv = contentEl.createDiv();
        apiKeyDiv.createEl("label", { text: "API密钥：" });
        const apiKeyInput = apiKeyDiv.createEl("input", { 
          type: "password", 
          placeholder: "请输入API密钥" 
        });
        apiKeyInput.style.width = "100%";
        apiKeyInput.style.marginBottom = "15px";
        
        // 模型选择
        const modelDiv = contentEl.createDiv();
        modelDiv.createEl("label", { text: "模型：" });
        const modelSelect = modelDiv.createEl("select");
        modelSelect.style.width = "100%";
        modelSelect.style.marginBottom = "15px";
        
        // 根据提供商更新模型选项
        const updateModelOptions = () => {
          modelSelect.innerHTML = "";
          if (providerSelect.value === "zhipu") {
            modelSelect.createEl("option", { value: "GLM-4-Flash", text: "GLM-4-Flash (免费)" });
            modelSelect.createEl("option", { value: "glm-4-plus", text: "GLM-4-Plus (付费)" });
          } else if (providerSelect.value === "deepseek") {
            modelSelect.createEl("option", { value: "deepseek-chat", text: "DeepSeek Chat" });
            modelSelect.createEl("option", { value: "deepseek-coder", text: "DeepSeek Coder" });
          }
        };
        
        providerSelect.addEventListener("change", updateModelOptions);
        updateModelOptions();
        
        // 输出文件夹
        const folderDiv = contentEl.createDiv();
        folderDiv.createEl("label", { text: "输出文件夹：" });
        const folderInput = folderDiv.createEl("input", { 
          type: "text", 
          placeholder: "atomic-notes",
          value: "atomic-notes"
        });
        folderInput.style.width = "100%";
        folderInput.style.marginBottom = "15px";
        
        // 自定义提示词
        const promptDiv = contentEl.createDiv();
        promptDiv.createEl("label", { text: "自定义提示词（可选）：" });
        const promptTextarea = promptDiv.createEl("textarea", { 
          placeholder: `留空使用默认提示词，或输入自定义提示词...

默认提示词专门针对配置文件优化：
- 分析配置结构和组件
- 提取关键概念而非复制原始JSON
- 生成易理解的说明文档`
        });
        promptTextarea.style.width = "100%";
        promptTextarea.style.height = "120px";
        promptTextarea.style.marginBottom = "20px";
        promptTextarea.style.resize = "vertical";
        
        // 按钮容器
        const buttonDiv = contentEl.createDiv();
        buttonDiv.style.textAlign = "right";
        
        const cancelButton = buttonDiv.createEl("button", { text: "取消" });
        cancelButton.style.marginRight = "10px";
        
        const confirmButton = buttonDiv.createEl("button", { text: "开始分割" });
        confirmButton.style.backgroundColor = "#007acc";
        confirmButton.style.color = "white";
        confirmButton.style.border = "none";
        confirmButton.style.padding = "8px 16px";
        confirmButton.style.borderRadius = "4px";
        
        // 取消按钮事件
        cancelButton.addEventListener("click", () => {
          this.close();
        });
        
        // 确认按钮事件
        confirmButton.addEventListener("click", async () => {
          const provider = providerSelect.value;
          const apiKey = apiKeyInput.value.trim();
          const model = modelSelect.value;
          const outputFolder = folderInput.value.trim() || "atomic-notes";
          const customPrompt = promptTextarea.value.trim();
          
          if (!apiKey) {
            new Notice("请输入API密钥");
            return;
          }
          
          this.close();
          
          // 执行分割
          await performConfigSplit(provider, apiKey, model, outputFolder, title, fileContent, customPrompt);
        });
      }
    }
    
    const modal = new AtomicNoteSplitterModal(app);
    modal.open();
    
  } catch (error) {
    new Notice("创建界面时出错：" + error.message);
    console.error("Modal creation error:", error);
  }
}

async function performConfigSplit(provider, apiKey, model, outputFolder, title, fileContent, customPrompt) {
  try {
    // 创建输出文件夹
    try {
      await app.vault.createFolder(outputFolder);
    } catch (e) {
      // 文件夹已存在，忽略错误
    }
    
    // 构建专门针对配置文件的提示词
    const defaultPrompt = `请分析以下配置文件内容，将其分解成易于理解的原子笔记。重要：不要复制原始JSON，而是要解释和总结。

文件名：${title}
配置内容：
${fileContent}

分析要求：
1. 识别配置的主要功能和用途
2. 解释关键配置项的作用
3. 总结布局和组件结构
4. 提取重要的参数和设置
5. 每个原子笔记应该是概念性的解释，不是原始数据的复制

返回简洁的JSON格式：
{"atomic_notes":[{"title":"配置概念标题","content":"用通俗语言解释这个配置的作用和意义","tags":["配置","组件"],"keywords":["关键词"]}]}`;

    const prompt = customPrompt || defaultPrompt;
    
    // 根据提供商构建API请求
    let apiOptions;
    
    if (provider === "zhipu") {
      apiOptions = {
        method: "POST",
        url: "https://open.bigmodel.cn/api/paas/v4/chat/completions",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          model: model,
          messages: [
            {
              role: "system",
              content: "你是一个专业的技术文档分析师。你的任务是将复杂的配置文件转换成易于理解的知识笔记。不要复制原始数据，而是要解释概念和用途。必须返回有效的JSON格式。"
            },
            {
              role: "user",
              content: prompt,
            },
          ],
          temperature: 0.3,
          max_tokens: 2000,
        }),
      };
    } else if (provider === "deepseek") {
      apiOptions = {
        method: "POST",
        url: "https://api.deepseek.com/chat/completions",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          model: model,
          messages: [
            {
              role: "system",
              content: "你是一个专业的技术文档分析师。你的任务是将复杂的配置文件转换成易于理解的知识笔记。不要复制原始数据，而是要解释概念和用途。必须返回有效的JSON格式。"
            },
            {
              role: "user",
              content: prompt,
            },
          ],
          temperature: 0.3,
          max_tokens: 2000,
        }),
      };
    }
    
    new Notice("正在分析配置文件...");
    console.log("发送API请求:", apiOptions);
    
    const response = await obsidian.requestUrl(apiOptions);
    const result = response.json;
    
    console.log("API响应:", result);
    
    if (!result.choices || result.choices.length === 0) {
      new Notice("AI分析失败 - 没有返回内容");
      console.error("API响应错误:", result);
      return;
    }

    const aiResponse = result.choices[0].message?.content;
    if (!aiResponse) {
      new Notice("AI分析失败 - 响应为空");
      console.error("AI响应为空:", result.choices[0]);
      return;
    }

    console.log("AI原始返回:", aiResponse);

    // 尝试解析JSON
    let atomicNotesData;
    try {
      let cleanResponse = aiResponse.trim();
      cleanResponse = cleanResponse.replace(/```json\s*/g, '').replace(/```\s*/g, '');
      
      const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanResponse = jsonMatch[0];
      }
      
      console.log("清理后的JSON:", cleanResponse);
      atomicNotesData = JSON.parse(cleanResponse);
      
    } catch (parseError) {
      new Notice("AI返回格式错误，使用备用分割方案...");
      console.error("JSON解析错误:", parseError);
      await createConfigSplit(title, fileContent, outputFolder);
      return;
    }

    if (!atomicNotesData.atomic_notes || !Array.isArray(atomicNotesData.atomic_notes)) {
      new Notice("AI响应数据格式不正确，使用备用方案...");
      await createConfigSplit(title, fileContent, outputFolder);
      return;
    }

    const atomicNotes = atomicNotesData.atomic_notes;
    new Notice(`发现 ${atomicNotes.length} 个配置概念，正在创建笔记...`);

    // 创建每个原子笔记文件
    let createdCount = 0;
    for (let i = 0; i < atomicNotes.length; i++) {
      const note = atomicNotes[i];
      
      if (!note.title || !note.content) {
        continue;
      }

      const safeTitle = note.title
        .replace(/[<>:"/\\|?*\n\r\t]/g, '_')
        .replace(/[_\s]+/g, '_')
        .replace(/^_+|_+$/g, '')
        .substring(0, 50);
      
      const fileName = `${safeTitle}.md`;
      const filePath = `${outputFolder}/${fileName}`;
      
      const currentTime = new Date().toISOString().split('T')[0];
      const tags = note.tags || [];
      const keywords = note.keywords || [];

      const noteContent = `---
title: "${note.title}"
source: "[[${title}]]"
tags: [${tags.map(tag => `"${tag}"`).join(', ')}]
keywords: [${keywords.map(keyword => `"${keyword}"`).join(', ')}]
created: ${currentTime}
type: config-analysis
provider: ${provider}
model: ${model}
---

# ${note.title}

${note.content}

---
*此笔记由AI分析配置文件 [[${title}]] 生成*
`;

      try {
        let finalPath = filePath;
        let counter = 1;
        while (await app.vault.adapter.exists(finalPath)) {
          const nameWithoutExt = safeTitle;
          finalPath = `${outputFolder}/${nameWithoutExt}_${counter}.md`;
          counter++;
        }
        
        await app.vault.create(finalPath, noteContent);
        createdCount++;
        
      } catch (createError) {
        console.error(`创建文件失败 ${filePath}:`, createError);
      }
    }

    new Notice(`✅ 成功创建了 ${createdCount} 个配置分析笔记！`);
    
  } catch (error) {
    new Notice("发生错误 - 请检查控制台");
    console.error("配置分析错误:", error);
  }
}

// 配置文件备用分割方案
async function createConfigSplit(title, fileContent, outputFolder) {
  try {
    new Notice("使用备用方案分析配置文件...");
    
    // 简单分析配置结构
    const notes = [];
    
    // 基本信息
    notes.push({
      title: `${title}_配置概览`,
      content: `# 配置文件概览

**文件名**: ${title}
**文件大小**: ${fileContent.length} 字符
**文件类型**: 配置文件

## 基本信息
这是一个配置文件，包含了系统或应用的设置信息。

## 内容结构
${fileContent.includes('components') ? '- 包含组件配置' : ''}
${fileContent.includes('layout') ? '- 包含布局设置' : ''}
${fileContent.includes('id') ? '- 包含ID标识符' : ''}
${fileContent.includes('mobile') ? '- 包含移动端配置' : ''}
${fileContent.includes('laptop') ? '- 包含桌面端配置' : ''}
`
    });
    
    // 如果包含组件信息
    if (fileContent.includes('components')) {
      notes.push({
        title: `${title}_组件结构`,
        content: `# 组件结构分析

这个配置文件定义了一个组件系统的结构。

## 主要特征
- 使用组件化架构
- 支持多设备布局（移动端和桌面端）
- 包含组件ID和布局信息

## 组件类型
${fileContent.includes('"type": "multi"') ? '- 多组件容器类型' : ''}
${fileContent.includes('layout') ? '- 响应式布局系统' : ''}
`
      });
    }
    
    let createdCount = 0;
    for (const note of notes) {
      const safeTitle = note.title
        .replace(/[<>:"/\\|?*\n\r\t]/g, '_')
        .replace(/[_\s]+/g, '_')
        .replace(/^_+|_+$/g, '')
        .substring(0, 50);
      
      const fileName = `${safeTitle}.md`;
      const filePath = `${outputFolder}/${fileName}`;
      
      const currentTime = new Date().toISOString().split('T')[0];
      const noteContent = `---
title: "${note.title}"
source: "[[${title}]]"
created: ${currentTime}
type: config-analysis-backup
---

${note.content}

---
*此笔记由备用分析方案生成*
`;

      try {
        let finalPath = filePath;
        let counter = 1;
        while (await app.vault.adapter.exists(finalPath)) {
          finalPath = `${outputFolder}/${safeTitle}_${counter}.md`;
          counter++;
        }
        
        await app.vault.create(finalPath, noteContent);
        createdCount++;
        
      } catch (createError) {
        console.error(`创建文件失败 ${filePath}:`, createError);
      }
    }
    
    new Notice(`✅ 使用备用方案创建了 ${createdCount} 个配置分析笔记！`);
    
  } catch (error) {
    new Notice("备用分割方案也失败了");
    console.error("备用分割错误:", error);
  }
}

exports.default = {
  entry: atomicNoteSplitterForConfig,
  name: "atomicNoteSplitterForConfig",
  description: `配置文件专用原子笔记分割器

专门针对JSON配置文件优化：
- 🔧 分析配置结构而非复制原始数据
- 📝 生成概念性解释文档
- 🎯 避免JSON截断问题
- 🛡️ 智能备用分割方案
- 💡 自定义提示词支持

使用方法：
\`atomicNoteSplitterForConfig()\`
`,
};
