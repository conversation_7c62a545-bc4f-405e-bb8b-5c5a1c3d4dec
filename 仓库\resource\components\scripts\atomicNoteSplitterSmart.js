async function atomicNoteSplitterSmart() {
  try {
    // 获取当前活动文件
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
      new Notice("请先打开一个笔记文件");
      return;
    }

    const fileContent = await app.vault.cachedRead(activeFile);
    const title = activeFile.basename;
    
    // 检查文件内容
    if (!fileContent || fileContent.trim().length < 50) {
      new Notice("文件内容太少，无法进行有效分割");
      return;
    }

    // 创建弹窗界面
    class AtomicNoteSplitterModal extends obsidian.Modal {
      constructor(app) {
        super(app);
      }
      
      onOpen() {
        this.titleEl.setText("智能原子笔记分割器");
        this.setupUI();
      }
      
      onClose() {
        this.contentEl.empty();
      }
      
      setupUI() {
        const contentEl = this.contentEl;
        contentEl.empty();
        contentEl.style.padding = "20px";
        contentEl.style.minWidth = "600px";
        
        // 显示当前文件信息
        const fileInfoDiv = contentEl.createDiv();
        fileInfoDiv.style.marginBottom = "15px";
        fileInfoDiv.style.padding = "10px";
        fileInfoDiv.style.backgroundColor = "#f0f0f0";
        fileInfoDiv.style.borderRadius = "4px";
        
        // 分析文件类型
        let fileType = "普通文本";
        if (fileContent.includes('"components"') && fileContent.includes('"layout"')) {
          fileType = "组件配置文件";
        } else if (fileContent.includes('{') && fileContent.includes('}')) {
          fileType = "JSON数据文件";
        }
        
        fileInfoDiv.innerHTML = `<strong>当前文件:</strong> ${title}<br><strong>内容长度:</strong> ${fileContent.length} 字符<br><strong>文件类型:</strong> ${fileType}`;
        
        // API提供商选择
        const providerDiv = contentEl.createDiv();
        providerDiv.createEl("label", { text: "API提供商：" });
        const providerSelect = providerDiv.createEl("select");
        providerSelect.style.width = "100%";
        providerSelect.style.marginBottom = "15px";
        providerSelect.createEl("option", { value: "zhipu", text: "智谱AI (GLM)" });
        providerSelect.createEl("option", { value: "deepseek", text: "DeepSeek" });
        
        // API密钥输入
        const apiKeyDiv = contentEl.createDiv();
        apiKeyDiv.createEl("label", { text: "API密钥：" });
        const apiKeyInput = apiKeyDiv.createEl("input", { 
          type: "password", 
          placeholder: "请输入API密钥" 
        });
        apiKeyInput.style.width = "100%";
        apiKeyInput.style.marginBottom = "15px";
        
        // 模型选择
        const modelDiv = contentEl.createDiv();
        modelDiv.createEl("label", { text: "模型：" });
        const modelSelect = modelDiv.createEl("select");
        modelSelect.style.width = "100%";
        modelSelect.style.marginBottom = "15px";
        
        // 根据提供商更新模型选项
        const updateModelOptions = () => {
          modelSelect.innerHTML = "";
          if (providerSelect.value === "zhipu") {
            modelSelect.createEl("option", { value: "GLM-4-Flash", text: "GLM-4-Flash (免费)" });
            modelSelect.createEl("option", { value: "glm-4-plus", text: "GLM-4-Plus (付费)" });
          } else if (providerSelect.value === "deepseek") {
            modelSelect.createEl("option", { value: "deepseek-chat", text: "DeepSeek Chat" });
            modelSelect.createEl("option", { value: "deepseek-coder", text: "DeepSeek Coder" });
          }
        };
        
        providerSelect.addEventListener("change", updateModelOptions);
        updateModelOptions();
        
        // 输出文件夹
        const folderDiv = contentEl.createDiv();
        folderDiv.createEl("label", { text: "输出文件夹：" });
        const folderInput = folderDiv.createEl("input", { 
          type: "text", 
          placeholder: "atomic-notes",
          value: "atomic-notes"
        });
        folderInput.style.width = "100%";
        folderInput.style.marginBottom = "15px";
        
        // 分析模式选择
        const modeDiv = contentEl.createDiv();
        modeDiv.createEl("label", { text: "分析模式：" });
        const modeSelect = modeDiv.createEl("select");
        modeSelect.style.width = "100%";
        modeSelect.style.marginBottom = "15px";
        modeSelect.createEl("option", { value: "smart", text: "智能分析 - AI深度理解内容" });
        modeSelect.createEl("option", { value: "local", text: "本地分析 - 不使用AI，基于规则分割" });
        
        // 自定义提示词
        const promptDiv = contentEl.createDiv();
        promptDiv.createEl("label", { text: "自定义提示词（仅AI模式）：" });
        const promptTextarea = promptDiv.createEl("textarea", { 
          placeholder: `留空使用默认提示词，或输入自定义提示词...

针对配置文件的默认提示词会：
1. 分析组件结构和功能
2. 解释布局系统
3. 提取关键配置参数
4. 生成易懂的文档说明`
        });
        promptTextarea.style.width = "100%";
        promptTextarea.style.height = "120px";
        promptTextarea.style.marginBottom = "20px";
        promptTextarea.style.resize = "vertical";
        
        // 根据模式显示/隐藏提示词
        modeSelect.addEventListener("change", () => {
          if (modeSelect.value === "local") {
            promptDiv.style.display = "none";
          } else {
            promptDiv.style.display = "block";
          }
        });
        
        // 按钮容器
        const buttonDiv = contentEl.createDiv();
        buttonDiv.style.textAlign = "right";
        
        const cancelButton = buttonDiv.createEl("button", { text: "取消" });
        cancelButton.style.marginRight = "10px";
        
        const confirmButton = buttonDiv.createEl("button", { text: "开始分割" });
        confirmButton.style.backgroundColor = "#007acc";
        confirmButton.style.color = "white";
        confirmButton.style.border = "none";
        confirmButton.style.padding = "8px 16px";
        confirmButton.style.borderRadius = "4px";
        
        // 取消按钮事件
        cancelButton.addEventListener("click", () => {
          this.close();
        });
        
        // 确认按钮事件
        confirmButton.addEventListener("click", async () => {
          const provider = providerSelect.value;
          const apiKey = apiKeyInput.value.trim();
          const model = modelSelect.value;
          const outputFolder = folderInput.value.trim() || "atomic-notes";
          const customPrompt = promptTextarea.value.trim();
          const mode = modeSelect.value;
          
          if (mode === "smart" && !apiKey) {
            new Notice("智能分析模式需要输入API密钥");
            return;
          }
          
          this.close();
          
          // 执行分割
          if (mode === "smart") {
            await performSmartSplit(provider, apiKey, model, outputFolder, title, fileContent, customPrompt);
          } else {
            await performLocalSplit(title, fileContent, outputFolder);
          }
        });
      }
    }
    
    const modal = new AtomicNoteSplitterModal(app);
    modal.open();
    
  } catch (error) {
    new Notice("创建界面时出错：" + error.message);
    console.error("Modal creation error:", error);
  }
}

// 智能AI分析
async function performSmartSplit(provider, apiKey, model, outputFolder, title, fileContent, customPrompt) {
  try {
    // 创建输出文件夹
    try {
      await app.vault.createFolder(outputFolder);
    } catch (e) {
      // 文件夹已存在，忽略错误
    }
    
    // 构建智能提示词
    const defaultPrompt = `请深入分析以下内容，将其分解成多个有价值的原子笔记。每个笔记应该包含独立的知识点或概念。

文件：${title}
内容：
${fileContent.length > 3000 ? fileContent.substring(0, 3000) + "...(内容已截取前3000字符)" : fileContent}

分析要求：
1. 如果是配置文件，请解释各个组件的作用和关系
2. 如果是技术文档，请提取关键概念和方法
3. 如果是数据结构，请说明结构的用途和设计思路
4. 每个原子笔记应该是独立的知识点
5. 用通俗易懂的语言解释技术概念
6. 避免直接复制原始数据，而是要解释和总结

请返回3-8个原子笔记，格式如下：
{"atomic_notes":[{"title":"具体的知识点标题","content":"详细的解释和说明，包含实际的知识内容","tags":["相关标签"],"keywords":["关键词"]}]}`;

    const prompt = customPrompt || defaultPrompt;
    
    // API请求配置
    let apiOptions;
    
    if (provider === "zhipu") {
      apiOptions = {
        method: "POST",
        url: "https://open.bigmodel.cn/api/paas/v4/chat/completions",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          model: model,
          messages: [
            {
              role: "system",
              content: "你是一个专业的知识分析师和技术文档专家。你的任务是将复杂的内容分解成易于理解的知识笔记。重点是解释概念、分析结构、提供见解，而不是简单复制原始数据。"
            },
            {
              role: "user",
              content: prompt,
            },
          ],
          temperature: 0.4,
          max_tokens: 3000,
        }),
      };
    } else if (provider === "deepseek") {
      apiOptions = {
        method: "POST",
        url: "https://api.deepseek.com/chat/completions",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          model: model,
          messages: [
            {
              role: "system",
              content: "你是一个专业的知识分析师和技术文档专家。你的任务是将复杂的内容分解成易于理解的知识笔记。重点是解释概念、分析结构、提供见解，而不是简单复制原始数据。"
            },
            {
              role: "user",
              content: prompt,
            },
          ],
          temperature: 0.4,
          max_tokens: 3000,
        }),
      };
    }
    
    new Notice("正在进行智能分析...");
    console.log("发送API请求");
    
    const response = await obsidian.requestUrl(apiOptions);
    const result = response.json;
    
    if (!result.choices || result.choices.length === 0) {
      new Notice("AI分析失败，切换到本地分析模式");
      await performLocalSplit(title, fileContent, outputFolder);
      return;
    }

    const aiResponse = result.choices[0].message?.content;
    if (!aiResponse) {
      new Notice("AI响应为空，切换到本地分析模式");
      await performLocalSplit(title, fileContent, outputFolder);
      return;
    }

    console.log("AI返回:", aiResponse);

    // 解析JSON
    let atomicNotesData;
    try {
      let cleanResponse = aiResponse.trim();
      cleanResponse = cleanResponse.replace(/```json\s*/g, '').replace(/```\s*/g, '');
      
      const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanResponse = jsonMatch[0];
      }
      
      atomicNotesData = JSON.parse(cleanResponse);
      
    } catch (parseError) {
      new Notice("AI返回格式错误，切换到本地分析模式");
      console.error("JSON解析错误:", parseError);
      await performLocalSplit(title, fileContent, outputFolder);
      return;
    }

    if (!atomicNotesData.atomic_notes || !Array.isArray(atomicNotesData.atomic_notes)) {
      new Notice("AI数据格式不正确，切换到本地分析模式");
      await performLocalSplit(title, fileContent, outputFolder);
      return;
    }

    // 创建笔记文件
    await createNoteFiles(atomicNotesData.atomic_notes, outputFolder, title, provider, model, "ai-analysis");
    
  } catch (error) {
    new Notice("智能分析出错，切换到本地分析模式");
    console.error("智能分析错误:", error);
    await performLocalSplit(title, fileContent, outputFolder);
  }
}

// 本地规则分析
async function performLocalSplit(title, fileContent, outputFolder) {
  try {
    new Notice("使用本地分析模式...");
    
    // 创建输出文件夹
    try {
      await app.vault.createFolder(outputFolder);
    } catch (e) {
      // 文件夹已存在，忽略错误
    }
    
    const notes = [];
    
    // 分析文件类型和内容
    if (fileContent.includes('"components"') && fileContent.includes('"layout"')) {
      // 组件配置文件分析
      notes.push({
        title: `${title} - 组件系统架构`,
        content: `# 组件系统架构分析

## 文件概述
- **文件名**: ${title}
- **类型**: 组件配置文件
- **用途**: 定义UI组件的布局和结构

## 核心特征
- 采用组件化架构设计
- 支持响应式布局（移动端和桌面端）
- 使用唯一ID标识每个组件
- 包含详细的位置和尺寸信息

## 技术要点
- 组件采用网格布局系统
- 支持多设备适配
- 配置驱动的UI渲染`,
        tags: ["组件", "架构", "配置"],
        keywords: ["组件", "布局", "响应式"]
      });
      
      // 分析布局系统
      if (fileContent.includes('"mobile"') && fileContent.includes('"laptop"')) {
        notes.push({
          title: `${title} - 响应式布局系统`,
          content: `# 响应式布局系统

## 布局特点
这个配置文件实现了一个响应式布局系统，支持不同设备的适配。

## 设备支持
- **移动端 (mobile)**: 针对手机等小屏设备优化
- **桌面端 (laptop)**: 针对电脑等大屏设备优化

## 布局参数
每个组件的布局包含以下参数：
- **x**: 水平位置
- **y**: 垂直位置  
- **w**: 宽度
- **h**: 高度

## 设计理念
通过为不同设备提供不同的布局配置，确保在各种屏幕尺寸下都能提供良好的用户体验。`,
          tags: ["响应式", "布局", "设备适配"],
          keywords: ["mobile", "laptop", "响应式", "布局"]
        });
      }
      
      // 分析组件ID系统
      notes.push({
        title: `${title} - 组件标识系统`,
        content: `# 组件标识系统

## ID管理
配置文件使用UUID（通用唯一标识符）来标识每个组件，确保组件的唯一性。

## 标识符特点
- 使用32位十六进制字符
- 格式：8-4-4-4-12的分组形式
- 全局唯一，避免冲突

## 组件关系
- 主容器组件包含多个子组件
- 每个子组件通过componentId引用
- 形成层次化的组件结构

## 实际应用
这种ID系统便于：
- 组件的动态加载和管理
- 组件状态的追踪
- 组件间的通信和交互`,
        tags: ["ID", "标识符", "组件管理"],
        keywords: ["UUID", "componentId", "标识符"]
      });
    } else {
      // 通用文本分析
      const paragraphs = fileContent.split('\n\n').filter(p => p.trim().length > 100);
      
      for (let i = 0; i < Math.min(paragraphs.length, 5); i++) {
        const paragraph = paragraphs[i].trim();
        const firstLine = paragraph.split('\n')[0];
        const noteTitle = firstLine.length > 50 ? firstLine.substring(0, 50) + "..." : firstLine;
        
        notes.push({
          title: `${title} - 片段${i + 1}: ${noteTitle}`,
          content: `# ${noteTitle}

${paragraph}

---
*摘自文件: ${title}*`,
          tags: ["文档片段"],
          keywords: ["内容", "片段"]
        });
      }
    }
    
    // 创建笔记文件
    await createNoteFiles(notes, outputFolder, title, "local", "rule-based", "local-analysis");
    
  } catch (error) {
    new Notice("本地分析也失败了");
    console.error("本地分析错误:", error);
  }
}

// 创建笔记文件的通用函数
async function createNoteFiles(notes, outputFolder, sourceTitle, provider, model, analysisType) {
  let createdCount = 0;
  
  for (const note of notes) {
    if (!note.title || !note.content) continue;
    
    const safeTitle = note.title
      .replace(/[<>:"/\\|?*\n\r\t]/g, '_')
      .replace(/[_\s]+/g, '_')
      .replace(/^_+|_+$/g, '')
      .substring(0, 60);
    
    const fileName = `${safeTitle}.md`;
    const filePath = `${outputFolder}/${fileName}`;
    
    const currentTime = new Date().toISOString().split('T')[0];
    const tags = note.tags || [];
    const keywords = note.keywords || [];

    const noteContent = `---
title: "${note.title}"
source: "[[${sourceTitle}]]"
tags: [${tags.map(tag => `"${tag}"`).join(', ')}]
keywords: [${keywords.map(keyword => `"${keyword}"`).join(', ')}]
created: ${currentTime}
type: ${analysisType}
provider: ${provider}
model: ${model}
---

${note.content}

---
*此笔记由智能分析系统生成，源文件: [[${sourceTitle}]]*
`;

    try {
      let finalPath = filePath;
      let counter = 1;
      while (await app.vault.adapter.exists(finalPath)) {
        const nameWithoutExt = safeTitle;
        finalPath = `${outputFolder}/${nameWithoutExt}_${counter}.md`;
        counter++;
      }
      
      await app.vault.create(finalPath, noteContent);
      createdCount++;
      
    } catch (createError) {
      console.error(`创建文件失败 ${filePath}:`, createError);
    }
  }
  
  new Notice(`✅ 成功创建了 ${createdCount} 个原子笔记！`);
}

exports.default = {
  entry: atomicNoteSplitterSmart,
  name: "atomicNoteSplitterSmart",
  description: `智能原子笔记分割器 - 双模式分析

特性：
- 🧠 智能AI分析模式 - 深度理解内容
- 🔧 本地规则分析模式 - 无需API也能工作
- 📝 针对配置文件优化的分析逻辑
- 🎯 自定义提示词支持
- 🛡️ 自动降级机制，确保总能生成结果

使用方法：
\`atomicNoteSplitterSmart()\`
`,
};
