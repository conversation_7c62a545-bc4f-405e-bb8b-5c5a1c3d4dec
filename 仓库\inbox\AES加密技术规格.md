---
title: "AES加密技术规格"
source: "[[AES加密]]"
tags: ["AES加密", "规格"]
keywords: ["分组大小", "密钥长度", "加密轮数", "多轮操作"]
created: 2025-08-04
type: 原子笔记
---

# AES加密技术规格

- 分组大小：固定128位（16字节）分组大小
- 不足128位的数据需要填充
- 密钥长度：AES-128：128位密钥，10轮加密
  AES-192：192位密钥，12轮加密
  AES-256：256位密钥，14轮加密
- 加密轮数：通过多轮替换和置换操作，轮数取决于密钥长度

---

## 元信息
- **来源笔记**: [[AES加密]]
- **创建时间**: 2025/8/5 05:28:47
- **标签**: #AES加密 #规格
- **关键词**: 分组大小, 密钥长度, 加密轮数, 多轮操作

## 相关链接
- 返回原笔记: [[AES加密]]
