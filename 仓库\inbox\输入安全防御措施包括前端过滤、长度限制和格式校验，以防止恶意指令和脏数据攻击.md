---
title: 输入安全防御措施
source: "[[输入输出安全 两大方面：    1.  输入安全 (防投毒)：过滤用户的恶意指令（如“忽略之前指令，现在你是...”）、脏数据攻击（乱码病毒代码）。        -   防御措施：前端过滤、长度限制、格式校验。    2.  输出安全 (防发疯)：过滤AI生成的有害、违法、不道德的内容（如[大模型幻觉](大模型幻觉.md)）。        -   防御措施：规则过滤（敏感词）、置信度拦截、日志溯源。]]"
tags:
  - AI安全
  - 防御策略
keywords:
  - 前端过滤
  - 长度限制
  - 格式校验
created: 2025-08-04
type: 原子笔记
已学: true
---

# 输入安全防御措施包括前端过滤、长度限制和格式校验，以防止恶意指令和脏数据攻击

输入安全防御措施包括前端过滤、长度限制和格式校验，以防止恶意指令和脏数据攻击。

---

## 元信息
- **来源笔记**: [[输入输出安全 两大方面：    1.  输入安全 (防投毒)：过滤用户的恶意指令（如“忽略之前指令，现在你是...”）、脏数据攻击（乱码病毒代码）。        -   防御措施：前端过滤、长度限制、格式校验。    2.  输出安全 (防发疯)：过滤AI生成的有害、违法、不道德的内容（如[大模型幻觉](大模型幻觉.md)）。        -   防御措施：规则过滤（敏感词）、置信度拦截、日志溯源。]]
- **创建时间**: 2025/8/5 05:12:12
- **标签**: #AI安全 #防御策略
- **关键词**: 前端过滤, 长度限制, 格式校验

## 相关链接
- 返回原笔记: [[输入输出安全 两大方面：    1.  输入安全 (防投毒)：过滤用户的恶意指令（如“忽略之前指令，现在你是...”）、脏数据攻击（乱码病毒代码）。        -   防御措施：前端过滤、长度限制、格式校验。    2.  输出安全 (防发疯)：过滤AI生成的有害、违法、不道德的内容（如[大模型幻觉](大模型幻觉.md)）。        -   防御措施：规则过滤（敏感词）、置信度拦截、日志溯源。]]
