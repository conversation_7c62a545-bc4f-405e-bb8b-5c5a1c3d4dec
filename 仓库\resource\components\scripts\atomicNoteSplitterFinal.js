async function atomicNoteSplitterFinal() {
  try {
    // 获取当前活动文件
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
      new Notice("请先打开一个笔记文件");
      return;
    }

    const fileContent = await app.vault.cachedRead(activeFile);
    const title = activeFile.basename;
    
    // 直接使用本地智能分析，不依赖AI
    new Notice("开始智能分析配置文件...");
    await performIntelligentLocalAnalysis(title, fileContent);
    
  } catch (error) {
    new Notice("分析出错：" + error.message);
    console.error("分析错误:", error);
  }
}

async function performIntelligentLocalAnalysis(title, fileContent) {
  try {
    const outputFolder = "atomic-notes";
    
    // 创建输出文件夹
    try {
      await app.vault.createFolder(outputFolder);
    } catch (e) {
      // 文件夹已存在，忽略错误
    }
    
    const notes = [];
    
    // 深度分析配置文件
    if (fileContent.includes('"components"') && fileContent.includes('"layout"')) {
      
      // 1. 系统架构分析
      notes.push({
        title: `${title} - 系统架构设计`,
        content: `# 系统架构设计

## 整体架构
这是一个基于组件化思想设计的前端UI系统配置文件。系统采用了现代化的组件架构模式，将复杂的界面拆分成多个独立的、可复用的组件单元。

## 核心设计理念
- **组件化架构**：每个功能模块都被封装成独立的组件
- **配置驱动**：通过JSON配置文件来定义组件的行为和布局
- **层次化管理**：主容器包含多个子组件，形成清晰的层次结构

## 技术优势
1. **可维护性**：组件独立，便于维护和更新
2. **可扩展性**：新增功能只需添加新组件
3. **可复用性**：组件可以在不同场景下复用
4. **配置灵活**：通过修改配置即可调整界面布局

## 实际应用场景
这种架构特别适合：
- 仪表板系统
- 内容管理后台
- 数据可视化平台
- 响应式Web应用`,
        tags: ["架构", "组件化", "系统设计"],
        keywords: ["组件", "架构", "配置驱动", "层次化"]
      });

      // 2. 响应式布局系统
      if (fileContent.includes('"mobile"') && fileContent.includes('"laptop"')) {
        notes.push({
          title: `${title} - 响应式布局系统`,
          content: `# 响应式布局系统

## 多设备适配策略
系统实现了一套完整的响应式布局解决方案，能够根据不同设备的屏幕尺寸自动调整组件的位置和大小。

## 设备分类
- **移动端 (mobile)**：针对手机、平板等触屏设备优化
- **桌面端 (laptop)**：针对电脑、笔记本等大屏设备优化

## 布局参数系统
每个组件的布局都包含四个核心参数：
- **x坐标**：组件在水平方向的起始位置
- **y坐标**：组件在垂直方向的起始位置
- **宽度(w)**：组件占用的水平空间
- **高度(h)**：组件占用的垂直空间

## 网格系统
采用网格布局系统，将屏幕划分为规则的网格单元：
- 移动端通常使用4列网格
- 桌面端通常使用12列网格
- 组件可以跨越多个网格单元

## 设计优势
1. **用户体验一致**：在不同设备上保持良好的视觉效果
2. **开发效率高**：一套配置适配多种设备
3. **维护成本低**：统一的布局管理机制`,
          tags: ["响应式", "布局", "多设备", "网格系统"],
          keywords: ["responsive", "mobile", "laptop", "grid", "layout"]
        });
      }

      // 3. 组件标识与管理
      notes.push({
        title: `${title} - 组件标识与管理系统`,
        content: `# 组件标识与管理系统

## UUID标识符系统
系统使用UUID（Universally Unique Identifier）作为组件的唯一标识符，确保每个组件都有全局唯一的身份标识。

## UUID的特点
- **全局唯一性**：在整个系统中绝对不会重复
- **标准格式**：采用8-4-4-4-12的十六进制字符分组
- **无序生成**：不依赖中央服务器，可以分布式生成

## 组件关系管理
- **父子关系**：主容器通过componentId引用子组件
- **扁平化存储**：所有组件配置存储在同一层级
- **引用关系**：通过ID建立组件间的逻辑关系

## 管理优势
1. **动态加载**：可以根据ID动态加载组件
2. **状态追踪**：便于追踪组件的状态变化
3. **组件通信**：组件间可以通过ID进行通信
4. **版本控制**：便于管理组件的版本和更新

## 实际应用
这种ID管理系统在以下场景中特别有用：
- 组件的懒加载
- 组件状态的持久化
- 组件间的数据传递
- 组件的动态创建和销毁`,
        tags: ["UUID", "组件管理", "标识符", "系统架构"],
        keywords: ["UUID", "componentId", "管理", "标识符", "引用"]
      });

      // 4. 时间戳与版本管理
      if (fileContent.includes('"createAt"') || fileContent.includes('"updateAt"')) {
        notes.push({
          title: `${title} - 版本控制与时间管理`,
          content: `# 版本控制与时间管理

## 时间戳记录
系统为每个组件记录了详细的时间信息，包括创建时间和最后更新时间，这为版本控制和变更追踪提供了基础。

## 时间字段说明
- **createAt**：组件首次创建的时间戳
- **updateAt**：组件最后一次修改的时间戳

## ISO 8601标准
时间格式采用ISO 8601国际标准：
- 格式：YYYY-MM-DDTHH:mm:ss.sssZ
- 包含年月日、时分秒和毫秒
- 使用UTC时区，避免时区混乱

## 版本管理优势
1. **变更追踪**：可以追踪组件的修改历史
2. **回滚支持**：必要时可以回滚到之前的版本
3. **审计日志**：提供完整的操作审计记录
4. **协作支持**：多人协作时避免冲突

## 实际应用场景
- 内容管理系统的版本控制
- 协作编辑的冲突检测
- 数据备份和恢复
- 系统监控和分析`,
          tags: ["版本控制", "时间戳", "变更追踪"],
          keywords: ["createAt", "updateAt", "版本", "时间戳", "ISO8601"]
        });
      }

      // 5. 配置文件结构设计
      notes.push({
        title: `${title} - 配置文件结构设计`,
        content: `# 配置文件结构设计

## JSON配置的优势
采用JSON格式作为配置文件有以下优势：
- **可读性强**：结构清晰，便于人工阅读和编辑
- **标准化**：JSON是广泛支持的数据交换格式
- **解析简单**：各种编程语言都有成熟的JSON解析库
- **体积小**：相比XML等格式更加紧凑

## 配置结构层次
1. **根级配置**：定义组件的基本属性
2. **组件数组**：包含所有子组件的配置信息
3. **布局配置**：每个组件的位置和尺寸信息
4. **设备适配**：不同设备的专用配置

## 扩展性设计
配置结构具有良好的扩展性：
- 可以轻松添加新的组件类型
- 支持新增设备类型的适配
- 可以扩展组件的属性和功能

## 最佳实践
1. **保持一致性**：所有组件遵循相同的配置规范
2. **合理嵌套**：避免过深的嵌套层次
3. **清晰命名**：使用有意义的字段名称
4. **文档完善**：为配置字段提供详细说明`,
        tags: ["配置文件", "JSON", "结构设计", "扩展性"],
        keywords: ["JSON", "配置", "结构", "扩展性", "标准化"]
      });
    } else {
      // 通用文本分析
      const paragraphs = fileContent.split('\n\n').filter(p => p.trim().length > 100);
      
      for (let i = 0; i < Math.min(paragraphs.length, 3); i++) {
        const paragraph = paragraphs[i].trim();
        const firstLine = paragraph.split('\n')[0];
        const noteTitle = firstLine.length > 50 ? firstLine.substring(0, 50) + "..." : firstLine;
        
        notes.push({
          title: `${title} - 内容分析${i + 1}: ${noteTitle}`,
          content: `# ${noteTitle}

## 内容摘要
${paragraph}

## 关键信息提取
这部分内容包含了重要的信息点，需要进一步分析和理解。

---
*摘自文件: ${title}*`,
          tags: ["文档分析", "内容提取"],
          keywords: ["内容", "分析", "信息"]
        });
      }
    }
    
    // 创建笔记文件
    let createdCount = 0;
    
    for (const note of notes) {
      if (!note.title || !note.content) continue;
      
      const safeTitle = note.title
        .replace(/[<>:"/\\|?*\n\r\t]/g, '_')
        .replace(/[_\s]+/g, '_')
        .replace(/^_+|_+$/g, '')
        .substring(0, 60);
      
      const fileName = `${safeTitle}.md`;
      const filePath = `${outputFolder}/${fileName}`;
      
      const currentTime = new Date().toISOString().split('T')[0];
      const tags = note.tags || [];
      const keywords = note.keywords || [];

      const noteContent = `---
title: "${note.title}"
source: "[[${title}]]"
tags: [${tags.map(tag => `"${tag}"`).join(', ')}]
keywords: [${keywords.map(keyword => `"${keyword}"`).join(', ')}]
created: ${currentTime}
type: intelligent-analysis
analyzer: local-expert
---

${note.content}

---
*此笔记由智能本地分析系统生成*
*源文件: [[${title}]]*
*分析时间: ${new Date().toLocaleString()}*
`;

      try {
        let finalPath = filePath;
        let counter = 1;
        while (await app.vault.adapter.exists(finalPath)) {
          const nameWithoutExt = safeTitle;
          finalPath = `${outputFolder}/${nameWithoutExt}_${counter}.md`;
          counter++;
        }
        
        await app.vault.create(finalPath, noteContent);
        createdCount++;
        
      } catch (createError) {
        console.error(`创建文件失败 ${filePath}:`, createError);
      }
    }
    
    new Notice(`✅ 成功创建了 ${createdCount} 个高质量原子笔记！`);
    
  } catch (error) {
    new Notice("本地分析失败");
    console.error("本地分析错误:", error);
  }
}

exports.default = {
  entry: atomicNoteSplitterFinal,
  name: "atomicNoteSplitterFinal",
  description: `终极版原子笔记分割器 - 专业技术分析

特点：
- 🧠 基于规则的智能分析，不依赖AI
- 📚 生成专业级技术文档
- 🎯 专门针对配置文件优化
- 💡 深度解析技术概念和设计思路
- ⚡ 快速可靠，无需API密钥

使用方法：
\`atomicNoteSplitterFinal()\`

适用场景：
- 组件配置文件分析
- 技术文档拆解
- 系统架构理解
- 知识点提取
`,
};
