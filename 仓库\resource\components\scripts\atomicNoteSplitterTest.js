async function atomicNoteSplitterTest() {
  try {
    new Notice("测试函数开始执行...");
    console.log("测试函数开始执行");
    
    // 获取当前活动文件
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
      new Notice("请先打开一个笔记文件");
      return;
    }

    const fileContent = await app.vault.cachedRead(activeFile);
    const title = activeFile.basename;
    
    new Notice(`正在分析文件: ${title}`);
    console.log(`文件内容长度: ${fileContent.length}`);
    
    // 简单测试分析
    await createTestNotes(title, fileContent);
    
  } catch (error) {
    new Notice("测试出错：" + error.message);
    console.error("测试错误:", error);
  }
}

async function createTestNotes(title, fileContent) {
  try {
    const outputFolder = "test-atomic-notes";
    
    // 创建输出文件夹
    try {
      await app.vault.createFolder(outputFolder);
      new Notice("文件夹创建成功");
    } catch (e) {
      new Notice("文件夹已存在或创建失败");
      console.log("文件夹错误:", e);
    }
    
    // 创建一个简单的测试笔记
    const testNote = {
      title: `${title} - 测试分析`,
      content: `# 测试分析结果

## 文件信息
- **文件名**: ${title}
- **内容长度**: ${fileContent.length} 字符
- **分析时间**: ${new Date().toLocaleString()}

## 内容类型分析
${fileContent.includes('"components"') ? '✅ 检测到组件配置' : '❌ 未检测到组件配置'}
${fileContent.includes('"layout"') ? '✅ 检测到布局信息' : '❌ 未检测到布局信息'}
${fileContent.includes('"mobile"') ? '✅ 检测到移动端配置' : '❌ 未检测到移动端配置'}
${fileContent.includes('"laptop"') ? '✅ 检测到桌面端配置' : '❌ 未检测到桌面端配置'}

## 基本分析
这是一个${fileContent.includes('components') ? '组件配置文件' : '普通文本文件'}。

${fileContent.includes('components') ? `
### 组件系统特征
- 使用JSON格式存储配置
- 包含组件布局信息
- 支持多设备适配
- 采用ID标识系统
` : ''}

## 内容预览
\`\`\`
${fileContent.substring(0, 200)}...
\`\`\`
`,
      tags: ["测试", "分析"],
      keywords: ["test", "analysis"]
    };
    
    // 创建笔记文件
    const safeTitle = testNote.title
      .replace(/[<>:"/\\|?*\n\r\t]/g, '_')
      .replace(/[_\s]+/g, '_')
      .replace(/^_+|_+$/g, '')
      .substring(0, 60);
    
    const fileName = `${safeTitle}.md`;
    const filePath = `${outputFolder}/${fileName}`;
    
    const currentTime = new Date().toISOString().split('T')[0];
    
    const noteContent = `---
title: "${testNote.title}"
source: "[[${title}]]"
tags: ["测试", "分析"]
keywords: ["test", "analysis"]
created: ${currentTime}
type: test-analysis
---

${testNote.content}

---
*此笔记由测试分析系统生成*
`;

    try {
      let finalPath = filePath;
      let counter = 1;
      while (await app.vault.adapter.exists(finalPath)) {
        finalPath = `${outputFolder}/${safeTitle}_${counter}.md`;
        counter++;
      }
      
      await app.vault.create(finalPath, noteContent);
      new Notice(`✅ 测试笔记创建成功: ${fileName}`);
      console.log("笔记创建成功:", finalPath);
      
    } catch (createError) {
      new Notice("创建笔记失败: " + createError.message);
      console.error("创建笔记失败:", createError);
    }
    
  } catch (error) {
    new Notice("创建测试笔记失败: " + error.message);
    console.error("创建测试笔记错误:", error);
  }
}

exports.default = {
  entry: atomicNoteSplitterTest,
  name: "atomicNoteSplitterTest",
  description: `测试版原子笔记分割器

用于测试基本功能是否正常：
- 文件读取
- 文件夹创建
- 笔记生成
- 错误处理

使用方法：
\`atomicNoteSplitterTest()\`
`,
};
