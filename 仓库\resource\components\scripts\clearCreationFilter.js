async function clearCreationFilter() {
  try {
    console.log("开始将创作筛选从true改为false");

    // 获取所有笔记文件
    const allFiles = app.vault.getMarkdownFiles();
    let foundFiles = [];
    let processedCount = 0;

    // 第一步：找到所有创作筛选为true的笔记
    for (const file of allFiles) {
      try {
        const cache = app.metadataCache.getFileCache(file);
        const frontmatter = cache?.frontmatter;

        // 只查找创作筛选为true的笔记
        if (frontmatter && frontmatter['创作筛选'] === true) {
          foundFiles.push({
            file: file,
            currentValue: frontmatter['创作筛选']
          });
          console.log(`找到文件: ${file.basename}, 当前值: ${frontmatter['创作筛选']}`);
        }
      } catch (error) {
        console.error(`检查文件失败 ${file.path}:`, error);
      }
    }

    if (foundFiles.length === 0) {
      new Notice("没有找到'创作筛选: true'的笔记");
      return;
    }
    
    // 第二步：将所有找到的文件的创作筛选从true改为false
    for (const item of foundFiles) {
      try {
        const file = item.file;
        const content = await app.vault.cachedRead(file);

        // 解析YAML frontmatter
        const frontmatterRegex = /^---\n([\s\S]*?)\n---/;
        const match = content.match(frontmatterRegex);

        if (match) {
          const frontmatterContent = match[1];
          const restContent = content.replace(frontmatterRegex, '').trim();

          // 将创作筛选从true改为false
          const lines = frontmatterContent.split('\n');
          const updatedLines = lines.map(line => {
            if (line.trim().startsWith('创作筛选:') || line.trim().startsWith('创作筛选 :')) {
              // 替换为false
              return line.replace(/创作筛选\s*:\s*true/i, '创作筛选: false');
            }
            return line;
          });

          // 重新构建文件内容
          const newFrontmatter = updatedLines.join('\n');
          const newContent = `---\n${newFrontmatter}\n---\n\n${restContent}`;

          // 写入修改后的内容
          await app.vault.modify(file, newContent);
          processedCount++;

          console.log(`✅ 已将 ${file.basename} 的创作筛选改为false`);

        } else {
          console.log(`⚠️ ${file.basename} 没有找到YAML frontmatter`);
        }

      } catch (error) {
        console.error(`处理文件失败 ${item.file.path}:`, error);
      }
    }
    
    new Notice(`✅ 修改完成！共将 ${processedCount} 个文件的创作筛选改为false`);
    console.log(`修改操作完成，共处理 ${processedCount} 个文件`);

    // 显示处理结果摘要
    const summary = `修改结果摘要：
- 找到true的文件数：${foundFiles.length}
- 成功改为false数：${processedCount}
- 失败数：${foundFiles.length - processedCount}`;

    console.log(summary);
    
  } catch (error) {
    new Notice("修改过程中发生错误 - 请检查控制台");
    console.error("修改创作筛选属性错误:", error);
  }
}

exports.default = {
  entry: clearCreationFilter,
  name: "clearCreationFilter",
  description: `一键取消创作筛选

功能：
- 🔍 自动搜索所有'创作筛选: true'的笔记
- 🔄 一键将所有true改为false
- ⚡ 直接执行，无需确认
- 📊 显示处理结果统计
- 🛡️ 保留其他YAML属性不变

使用方法：
\`clearCreationFilter()\`

注意事项：
- 此操作会将'创作筛选: true'改为'创作筛选: false'
- 不会删除创作筛选属性，只是改变值
- 其他YAML属性不会受影响
- 直接执行，无确认对话框

特性：
- 快速执行，一键完成
- 详细的控制台日志记录
- 错误处理和异常保护
- 显示处理结果统计
`,
};
