{"components": [{"id": "a65ef6ee-fd1a-4c91-b615-3c57f4a0948d", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2025-03-25T13:04:09.233Z", "updateAt": "2025-03-25T13:04:09.233Z", "components": [{"componentId": "1d80d70b-dce3-4ba5-a661-b743e067e3ec"}, {"componentId": "1e7f877e-9799-4c09-b691-92d2cd440167"}, {"componentId": "c4e8de2a-f395-4ab4-91cc-a8291ab49a3a"}, {"componentId": "bf2a223b-b0ea-4896-8516-de708e57cb5b"}, {"componentId": "096825b6-63ab-4590-9373-63ff40cfa3c4"}, {"componentId": "b7abdfcd-6983-4971-8122-16d724c03e30"}], "layoutType": "tab", "locked": false, "layoutOptions": {}}, {"id": "1d80d70b-dce3-4ba5-a661-b743e067e3ec", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "学习", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date: YYMMDD}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "549.7142944335938", "pinned": "left", "statisticType": "countUnique"}, "alias": "File Name"}, {"id": "edadd273-0d17-4654-8fe6-a64bcf5f437c", "name": " 🪚", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "atomicNoteSplitterDeepSeek()"}, "width": "59"}}, {"id": "6ad6aeb1-4693-4d98-93fa-449ddcf23064", "name": "👨", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "quickAI_MultiAPI(\"sk-b37b6de26d854175a1db8d6dfe1ea0ec\", \"对这篇笔记详细解释说人话，补充进正文，元信息和来源不要加了已经重复了，放到笔记一开始\", \"content\", \"deepseek\")"}, "width": "62"}, "alias": "人话"}, {"id": "e5b33e67-4c6f-47fd-b3e8-210862006808", "name": "学了", "isShow": true, "type": "button", "options": {"action": {"type": "updateProperty", "properties": [{"id": "e11d1cc9-ca51-4add-a10a-f1e161f27aad", "name": "已学", "type": "boolean", "value": true, "valueSource": "constant", "modifier": "replaceValue"}]}, "width": "66"}}, {"id": "4a9df847-03be-4613-a8d4-a7eb4b8b308c", "name": "已学", "isShow": true, "type": "checkbox", "options": {"width": "43"}}, {"id": "f58c4b71-b4d7-4dd2-af9a-69ebc7549e3c", "name": "重要", "isShow": true, "type": "button", "options": {"action": {"type": "updateProperty", "properties": [{"id": "e14774d2-c813-4367-b366-954a205e9ba6", "name": "🍎重要", "type": "boolean", "value": true, "valueSource": "constant", "modifier": "replaceValue"}]}, "width": "65"}}, {"id": "6a655545-6ffe-49fb-8830-95ad9800f6f5", "name": "📒学习笔记", "isShow": true, "type": "checkbox", "options": {"width": "111"}}, {"id": "34648f07-74a9-4f58-bd57-450674958664", "name": "AI", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "quickAI_MultiAPI(\"sk-b37b6de26d854175a1db8d6dfe1ea0ec\", \"对这篇笔记详细解释，补充进正文，元信息和来源不要加了已经重复了，放到笔记一开始\", \"content\", \"deepseek\")"}, "width": "52"}}, {"id": "93563940-d227-48e4-9ee4-5fa80039ae71", "name": "🍎重要", "isShow": true, "type": "checkbox", "options": {"width": "73"}}], "templates": [], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "newPageLocation": {"location": "inbox"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "and", "conditions": [{"id": "4a6d31f2-8a66-48c5-91ff-769ff90eca07", "type": "filter", "operator": "not_contains", "property": "${file.basename}", "value": "原子笔记索引", "conditions": []}, {"id": "d455b4f2-0da5-439f-9fca-97adecb24870", "type": "filter", "operator": "contains", "property": "${file.extension}", "value": "md", "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}}, {"id": "c4e8de2a-f395-4ab4-91cc-a8291ab49a3a", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "🍎重要", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date: YYMMDD}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "455", "pinned": "left", "statisticType": "hasValue"}, "alias": "File Name"}, {"id": "82c7e60c-f383-4412-89ee-a929cc60d437", "name": "AI总结", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "aiSummary(\"a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN\", \"总结\")"}, "width": "92"}}, {"id": "80f735b0-4715-427a-8f1e-9f99bedb0657", "name": "总结", "isShow": true, "type": "text", "options": {"width": "407"}}, {"id": "b6a6ee2c-8706-4bef-b2bf-16f52233378e", "name": "已学", "isShow": false, "type": "checkbox", "options": {}}, {"id": "4b4a6377-4d3a-4caf-b01a-704c53506b38", "name": "🍎重要", "isShow": true, "type": "checkbox", "options": {}}, {"id": "b7444778-be4f-47a2-96a4-b98a6238cca0", "name": "📒学习笔记", "isShow": false, "type": "checkbox", "options": {}}], "templates": [], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "newPageLocation": {"location": "inbox"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "and", "conditions": [{"id": "eb5614fc-7545-41cd-8ea2-c67b99e27e14", "type": "filter", "operator": "checked", "property": "🍎重要", "value": "", "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}}, {"id": "bf2a223b-b0ea-4896-8516-de708e57cb5b", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "📒已学", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date: YYMMDD}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "815.7142944335938", "pinned": "left", "statisticType": "hasValue"}, "alias": "File Name"}, {"id": "a83398a2-dac3-4c02-af6d-1960eef68b4c", "name": "已学", "isShow": true, "type": "checkbox", "options": {"width": "62"}}, {"id": "f58c4b71-b4d7-4dd2-af9a-69ebc7549e3c", "name": "重要", "isShow": true, "type": "button", "options": {"action": {"type": "updateProperty", "properties": [{"id": "e14774d2-c813-4367-b366-954a205e9ba6", "name": "🍎重要", "type": "boolean", "value": true, "valueSource": "constant", "modifier": "replaceValue"}]}, "width": "65"}}, {"id": "93563940-d227-48e4-9ee4-5fa80039ae71", "name": "🍎重要", "isShow": true, "type": "checkbox", "options": {"width": "88"}}, {"id": "9e43ea06-a938-4db2-9dbf-2be38637ed06", "name": "📒学习笔记", "isShow": false, "type": "checkbox", "options": {}}], "templates": [], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "newPageLocation": {"location": "inbox"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "and", "conditions": [{"id": "24a116f1-cc06-4974-aeb0-dcd0ee6f1c65", "type": "filter", "operator": "checked", "property": "已学", "value": "", "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "已学", "direction": "desc", "disabled": false}, {"id": "4d690858-07f2-4b44-9fcd-1b9ad9ef43cf", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}}, {"id": "096825b6-63ab-4590-9373-63ff40cfa3c4", "type": "card", "titleAlign": "center", "tabTitle": "新笔记", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2025-07-31T10:59:36.272Z", "updateAt": "2025-07-31T10:59:36.272Z", "title": "新笔记", "description": "", "coverFit": "cover", "coverPosition": "left", "clickAction": {"type": "CreateFile", "id": "ab78981d-c0e7-4134-b64a-eeb6d830b459", "options": {"targetFolder": "inbox", "openPageIn": "tab"}}, "cover": "resource/images/熊猫吃饭.png"}, {"id": "1e7f877e-9799-4c09-b691-92d2cd440167", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "最近", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-25T13:04:36.133Z", "updateAt": "2025-03-25T13:04:36.133Z", "viewType": "table", "newPageNameFormat": "{{date:MMDD HH:mm}}", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": true, "options": {"width": "867.7142944335938", "pinned": "left", "statisticType": "countUnique"}, "alias": "File Name"}, {"id": "e5b33e67-4c6f-47fd-b3e8-210862006808", "name": "学了", "isShow": true, "type": "button", "options": {"action": {"type": "updateProperty", "properties": [{"id": "e11d1cc9-ca51-4add-a10a-f1e161f27aad", "name": "已学", "type": "boolean", "value": true, "valueSource": "constant", "modifier": "replaceValue"}]}, "width": "66"}}, {"id": "4a9df847-03be-4613-a8d4-a7eb4b8b308c", "name": "已学", "isShow": true, "type": "checkbox", "options": {"width": "44"}}, {"id": "edadd273-0d17-4654-8fe6-a64bcf5f437c", "name": " 🪚", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "atomicNoteSplitter(\"a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN\", \"inbox\")"}, "width": "65"}}], "templates": [{"id": "c027b5fa-af9b-452e-aa56-cecf838bc352", "path": "TestFolder/Templates/New Project.md", "name": "New Project.md", "type": "templater"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": true, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "newPageLocation": {"location": "01-Projects"}, "loadLimitPerPage": 25, "filter": {"id": "3ce56217-e3bd-445c-9749-ee5988f8338f", "type": "group", "operator": "and", "conditions": [{"id": "66530bba-7efe-44be-b640-4a6627996196", "type": "filter", "operator": "contains", "property": "${file.tags}", "value": "", "conditions": []}, {"id": "a71a132b-d530-4c5c-8d18-99434a596d68", "type": "filter", "operator": "contains", "property": "${file.extension}", "value": "md", "conditions": []}]}, "sort": {"orders": [{"id": "fcfa6709-4d24-4c30-bf9c-bfea5ff6ab10", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}, "defaultTemplate": "c027b5fa-af9b-452e-aa56-cecf838bc352"}, {"id": "b7abdfcd-6983-4971-8122-16d724c03e30", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "删", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-07-31T19:27:09.648Z", "updateAt": "2025-07-31T19:27:09.648Z", "viewType": "table", "newPageNameFormat": "{{date:YYYYMMDDHHmmss}} ", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "文件名", "type": "text", "isShow": true, "wrap": false, "options": {"width": "708"}}], "templates": [], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "items": [], "pinFiltersToMenuBar": false, "showGrid": false, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "filter": {"id": "31742bd1-a87e-48b0-975e-9dcdface1ccd", "type": "group", "operator": "or", "conditions": [{"id": "b738a8ff-ea45-4549-bce0-66e5047c002f", "type": "filter", "operator": "contains", "property": "${file.basename}", "value": "原子笔记索引", "conditions": []}, {"id": "ffa547d6-75fc-474a-aa78-94443f850e03", "type": "filter", "operator": "contains", "property": "${file.basename}", "value": "upload", "conditions": []}]}}], "rootComponentId": "a65ef6ee-fd1a-4c91-b615-3c57f4a0948d"}