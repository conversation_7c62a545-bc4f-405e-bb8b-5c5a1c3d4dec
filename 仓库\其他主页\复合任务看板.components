{"components": [{"id": "c4175b13-d2ec-43d6-bff1-a06dca6709d9", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2025-03-26T14:42:25.662Z", "updateAt": "2025-03-26T14:42:25.662Z", "components": [{"componentId": "2b9d5efd-3f4e-444e-a835-a688a501d6db"}, {"componentId": "23a2cb94-68b0-4ebc-a270-23c7fd106da9"}, {"componentId": "ac5305bd-8729-4cca-8d0d-7df8f1208719"}], "layoutType": "tab", "locked": false, "layoutOptions": {}}, {"id": "ac5305bd-8729-4cca-8d0d-7df8f1208719", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "Daily Notes", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-03-26T14:50:26.170Z", "updateAt": "2025-03-26T14:50:26.170Z", "viewType": "calendar", "newPageNameFormat": "{{date:YYYYMMDDHHmmss}} ", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "File Basename", "type": "text", "isShow": true, "wrap": false, "options": {}}], "templates": [{"id": "8c13599c-5e7a-4e4b-b598-a244c155fecf", "path": "TestFolder/Templates/Daily Notes.md", "name": "Daily Notes.md", "type": "templater"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "itemSize": "components--page-card-medium", "showPropertyName": false, "showAllProperties": false, "items": [], "cover": {"type": "textContent", "source": "specifySectionContent", "fit": "contains", "layout": "components--page-card-cover-landscape", "position": "top", "sourceValue": "## ☑️Tasks"}, "pinFiltersToMenuBar": false, "showGrid": false, "heightType": "auto", "heightValue": 600, "calendarViewType": "month", "cardColorFrom": "default", "dateProperty": "${file.basename}"}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "defaultTemplate": "8c13599c-5e7a-4e4b-b598-a244c155fecf"}, {"id": "2b9d5efd-3f4e-444e-a835-a688a501d6db", "type": "dataview", "titleAlign": "center", "tabTitle": "Tasks Calendar", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2025-03-26T15:56:08.329Z", "updateAt": "2025-03-26T15:56:08.329Z", "query": "await \ndv.view(\"taskscalendar\", {\npages: \"\", \nview: \"week\", \nfirstDayOfWeek: \"1\",\ndailyNoteFolder: \"日志/Daily Notes\",\ndailyNoteFormat: \"YYYY-MM-DD\", \noptions: \"style9 noCellNameEvent noWeekNr noLineClamp\"\n})\n", "queryType": "dataviewjs", "maxHeight": 650, "dynamicParamComponents": []}, {"id": "23a2cb94-68b0-4ebc-a270-23c7fd106da9", "type": "multi", "titleAlign": "center", "tabTitle": "Matrix", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2025-03-26T16:02:05.844Z", "updateAt": "2025-03-26T16:02:05.844Z", "components": [{"componentId": "a590e343-90b7-49d9-b144-11fe1fccdfdb", "layout": {"mobile": {"x": 0, "y": 0, "w": 4, "h": 12}, "laptop": {"x": 0, "y": 0, "w": 6, "h": 15}}}, {"componentId": "4e04ced6-9849-426d-90b8-a513cc73fa27", "layout": {"mobile": {"x": 0, "y": 12, "w": 4, "h": 12}, "laptop": {"x": 6, "y": 0, "w": 6, "h": 15}}}, {"componentId": "c5a0df74-ea57-4c91-8b71-05a5b2135b03", "layout": {"mobile": {"x": 0, "y": 36, "w": 4, "h": 12}, "laptop": {"x": 6, "y": 15, "w": 6, "h": 15}}}, {"componentId": "06702b05-7dbf-43b3-b3df-462a79afdac4", "layout": {"mobile": {"x": 0, "y": 24, "w": 4, "h": 12}, "laptop": {"x": 0, "y": 15, "w": 6, "h": 15}}}], "layoutType": "grid", "locked": true, "layoutOptions": {}}, {"id": "a590e343-90b7-49d9-b144-11fe1fccdfdb", "type": "dataview", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2025-03-26T16:02:12.085Z", "updateAt": "2025-03-26T16:02:12.085Z", "query": "TASK\nFrom -\"TestFolder\" AND -\"#project/archived\"\nWHERE !completed\nWHERE contains(text, \"🔺\") OR contains(text, \"⏫\") OR contains(text, \"🔼\")\nWHERE (scheduled AND scheduled <= date(tomorrow)) OR (due AND due <= date(tomorrow)) OR (start AND start <= date(tomorrow))\nSort start DESC\nSort scheduled DESC\nSort due DESC", "queryType": "dataview", "maxHeight": 280, "dynamicParamComponents": [], "title": "Pinned", "backgroundColor": "#f6bcbc", "darkBackgroundColor": "#fda1a1"}, {"id": "4e04ced6-9849-426d-90b8-a513cc73fa27", "type": "dataview", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2025-03-26T16:03:11.117Z", "updateAt": "2025-03-26T16:03:11.117Z", "query": "TASK\nFrom -\"TestFolder\" AND -\"#exclude\"\nWHERE !completed\nWHERE contains(text, \"🔺\") OR contains(text, \"⏫\") OR contains(text, \"🔼\")\nWHERE (!scheduled AND !due AND !start) OR (scheduled AND scheduled > date(tomorrow)) OR (due AND due > date(tomorrow)) OR (start AND start > date(tomorrow))\nSort start DESC\nSort scheduled DESC\nSort due DESC", "queryType": "dataview", "maxHeight": 280, "dynamicParamComponents": [], "title": "InBox", "backgroundColor": "#fff0c9", "darkBackgroundColor": "#ffe6a2"}, {"id": "c5a0df74-ea57-4c91-8b71-05a5b2135b03", "type": "dataview", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2025-03-26T16:05:02.893Z", "updateAt": "2025-03-26T16:05:02.893Z", "query": "TASK\nFrom -\"#exclude\" AND -\"TestFolder\" AND -\"02-Areas/003-项目/Obsidian Handbook/_attachments/AnuPpuccin Check Boxs\"\nWHERE !checked\nWHERE !(contains(text, \"🔺\") OR contains(text, \"⏫\") OR contains(text, \"🔼\"))\nWHERE (contains(text, \"🔽\") OR contains(text, \"⏬\")) OR ((!scheduled AND !due AND !start) OR (scheduled AND scheduled > date(tomorrow)) OR (due AND due > date(tomorrow)) OR (start AND start > date(tomorrow)))\nSort start DESC\nSort scheduled DESC\nSort due DESC", "queryType": "dataview", "maxHeight": 280, "dynamicParamComponents": [], "title": "Odds", "backgroundColor": "#b9e6d3", "darkBackgroundColor": "#b9e6d3"}, {"id": "06702b05-7dbf-43b3-b3df-462a79afdac4", "type": "dataview", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2025-03-26T16:06:20.955Z", "updateAt": "2025-03-26T16:06:20.955Z", "query": "TASK\nFrom -\"#exclude\" AND -\"TestFolder\" \nWHERE !completed\nWHERE !(contains(text, \"🔺\") OR contains(text, \"⏫\") OR contains(text, \"🔼\") OR contains(text, \"🔽\") OR contains(text, \"⏬\")) \nWHERE (scheduled AND scheduled <= date(tomorrow)) OR (due AND due <= date(tomorrow)) OR (start AND start <= date(tomorrow))\nSort start DESC\nSort scheduled DESC\nSort due DESC", "queryType": "dataview", "maxHeight": 280, "dynamicParamComponents": [], "title": "To-Dos", "backgroundColor": "#a9cde1", "darkBackgroundColor": "#74bae1"}], "rootComponentId": "c4175b13-d2ec-43d6-bff1-a06dca6709d9"}