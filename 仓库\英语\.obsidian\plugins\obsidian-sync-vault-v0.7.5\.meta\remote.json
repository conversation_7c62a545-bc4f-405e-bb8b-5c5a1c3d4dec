{"name": "英语", "md5": "", "children": [{"name": "inbox", "type": "directory", "size": 0, "mtime": 1754304305000, "children": [{"name": "emerging_threats（新威胁）_原子笔记索引.md", "type": "file", "size": 1576, "mtime": 1754258930000}, {"name": "处理（Processing）.md", "type": "file", "size": 934, "mtime": 1753994480000}, {"name": "处理者（Processor）.md", "type": "file", "size": 911, "mtime": 1753994480000}, {"name": "动作-数据控制者implement、ensure、maintain、document、report、notify、obtain、conduct、assess、review.md", "type": "file", "size": 2497, "mtime": 1753997072000}, {"name": "高风险数据实践的复杂性认知削弱.md", "type": "file", "size": 779, "mtime": 1754257521000}, {"name": "个人数据（Personal_Data）.md", "type": "file", "size": 962, "mtime": 1754257603000}, {"name": "共同控制者（Joint_Controllers）.md", "type": "file", "size": 928, "mtime": 1753994480000}, {"name": "合法利益（Legitimate_Interest）.md", "type": "file", "size": 922, "mtime": 1753994480000}, {"name": "合规特权的认知削弱.md", "type": "file", "size": 689, "mtime": 1754257521000}, {"name": "合规文化中的大脑适应性盲点.md", "type": "file", "size": 737, "mtime": 1754257521000}, {"name": "假名化（Pseudonymization）.md", "type": "file", "size": 916, "mtime": 1753994480000}, {"name": "监管机构（Supervisory_Authority）.md", "type": "file", "size": 959, "mtime": 1753994480000}, {"name": "控制者（Controller）.md", "type": "file", "size": 933, "mtime": 1753994480000}, {"name": "跨境传输（Cross-border_Transfer）.md", "type": "file", "size": 968, "mtime": 1753994498000}, {"name": "例句1：网络攻击带来的新兴威胁.md", "type": "file", "size": 800, "mtime": 1754258930000}, {"name": "例句2：生物多样性面临的新兴威胁.md", "type": "file", "size": 830, "mtime": 1754258930000}, {"name": "敏感数据（Sensitive_Data）.md", "type": "file", "size": 956, "mtime": 1753994480000}, {"name": "模仿欧盟官方文件学习数据合规英语.md", "type": "file", "size": 868, "mtime": 1753994049000}, {"name": "匿名化（Anonymization）.md", "type": "file", "size": 925, "mtime": 1753994480000}, {"name": "强调-明确责任方、法律义务承担者、合规责任人等.md", "type": "file", "size": 763, "mtime": 1753957595000}, {"name": "删除权（Right_to_Erasure）.md", "type": "file", "size": 924, "mtime": 1753994480000}, {"name": "实战案例演示.md", "type": "file", "size": 1017, "mtime": 1753994482000}, {"name": "实战案例演示：起草隐私政策时描述数据主体权利.md", "type": "file", "size": 1150, "mtime": 1753994480000}, {"name": "术语库使用指南.md", "type": "file", "size": 1326, "mtime": 1753994480000}, {"name": "术语库使用指南_1.md", "type": "file", "size": 1376, "mtime": 1753994482000}, {"name": "数据保护影响评估（DPIA）.md", "type": "file", "size": 949, "mtime": 1753994480000}, {"name": "数据合规标准术语库（中英对照）.md", "type": "file", "size": 2987, "mtime": 1753994482000}, {"name": "数据合规英语长句拆解模板.md", "type": "file", "size": 927, "mtime": 1753994191000}, {"name": "数据合规英语的刻意练习方法.md", "type": "file", "size": 865, "mtime": 1753994050000}, {"name": "数据合规英语术语库（注：✅=推荐使用 ❌=禁止使用 ⚠️=需谨慎使用）.md", "type": "file", "size": 4100, "mtime": 1753997078000}, {"name": "数据合规英语术语库概述.md", "type": "file", "size": 1435, "mtime": 1753994480000}, {"name": "数据合规英语术语库建设重要性.md", "type": "file", "size": 1066, "mtime": 1753994482000}, {"name": "数据合规英语条款改写训练.md", "type": "file", "size": 925, "mtime": 1753997057000}, {"name": "数据合规英语学习资源推荐.md", "type": "file", "size": 793, "mtime": 1753994050000}, {"name": "数据合规英语中的逻辑简洁性.md", "type": "file", "size": 886, "mtime": 1753994049000}, {"name": "数据合规英语中的术语一致性.md", "type": "file", "size": 869, "mtime": 1753994049000}, {"name": "数据合规英语中的条件句嵌套.md", "type": "file", "size": 952, "mtime": 1753994168000}, {"name": "数据合规英语中的主谓一致.md", "type": "file", "size": 891, "mtime": 1753994166000}, {"name": "数据合规中的特权悖论.md", "type": "file", "size": 743, "mtime": 1754257521000}, {"name": "数据控制者报告数据泄露.md", "type": "file", "size": 1021, "mtime": 1753960352000}, {"name": "数据控制者获取敏感数据同意.md", "type": "file", "size": 1049, "mtime": 1753960352000}, {"name": "数据控制者记录数据收集目的.md", "type": "file", "size": 1018, "mtime": 1753960352000}, {"name": "数据控制者评估新数据处理活动风险.md", "type": "file", "size": 1073, "mtime": 1753960352000}, {"name": "数据控制者确保第三方遵守法规.md", "type": "file", "size": 1100, "mtime": 1753960352000}, {"name": "数据控制者审查隐私政策.md", "type": "file", "size": 1002, "mtime": 1753960352000}, {"name": "数据控制者实施安全措施.md", "type": "file", "size": 1039, "mtime": 1753960351000}, {"name": "数据控制者通知高风险数据泄露受影响个人.md", "type": "file", "size": 1137, "mtime": 1753960352000}, {"name": "数据控制者维护处理活动记录.md", "type": "file", "size": 1032, "mtime": 1753960352000}, {"name": "数据控制者执行数据保护影响评估.md", "type": "file", "size": 1073, "mtime": 1753960352000}, {"name": "数据泄露（Data_Breach）.md", "type": "file", "size": 900, "mtime": 1753994480000}, {"name": "数据主体（Data_Subject）.md", "type": "file", "size": 1055, "mtime": 1753994480000}, {"name": "同意（Consent）.md", "type": "file", "size": 915, "mtime": 1753994480000}, {"name": "相关概念.md", "type": "file", "size": 714, "mtime": 1753957096000}, {"name": "新兴威胁的出错点.md", "type": "file", "size": 853, "mtime": 1754258930000}, {"name": "新兴威胁的定义.md", "type": "file", "size": 794, "mtime": 1754259247000}, {"name": "新兴威胁的定义_原子笔记索引.md", "type": "file", "size": 703, "mtime": 1754259246000}, {"name": "新兴威胁的领域.md", "type": "file", "size": 979, "mtime": 1754259246000}, {"name": "新兴威胁的语法要点.md", "type": "file", "size": 864, "mtime": 1754258930000}, {"name": "新兴威胁概述.md", "type": "file", "size": 911, "mtime": 1754259246000}, {"name": "责任主体类型.md", "type": "file", "size": 801, "mtime": 1753957096000}, {"name": "责任主体强调定义.md", "type": "file", "size": 755, "mtime": 1753957096000}, {"name": "责任主体强调记忆要点.md", "type": "file", "size": 759, "mtime": 1753957096000}, {"name": "责任主体强调使用场景.md", "type": "file", "size": 730, "mtime": 1753957096000}, {"name": "责任主体强调示例.md", "type": "file", "size": 721, "mtime": 1753957096000}, {"name": "治理中非凡事物的常态化.md", "type": "file", "size": 752, "mtime": 1754257521000}]}, {"name": "Tags", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754304429000, "type": "directory", "children": [{"name": "<PERSON><PERSON><PERSON>", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754308696302, "type": "directory", "children": [{"name": "addBulletPoints.js", "type": "file", "size": 2338, "mtime": 1753067290000}, {"name": "aifront(1).js", "type": "file", "size": 5335, "mtime": 1752707735000}, {"name": "aifront.js", "type": "file", "size": 5340, "mtime": 1752713631000}, {"name": "aiSummary (1).js", "type": "file", "size": 2305, "mtime": 1749277202000}, {"name": "atomicNoteSplitter.js", "type": "file", "size": 8100, "mtime": 1753957417000}, {"name": "batchAI.js", "type": "file", "size": 10478, "mtime": 1754019616000}, {"name": "batchCreateCorpus.js", "type": "file", "size": 12987, "mtime": 1754191161000}, {"name": "calculatePracticeProgress.js", "type": "file", "size": 5893, "mtime": 1754093002000}, {"name": "calculateSleepQuality.js", "type": "file", "size": 3135, "mtime": 1753079685000}, {"name": "calculateSleepQualityPro.js", "type": "file", "size": 3248, "mtime": 1753079601000}, {"name": "calculatesSleepTime.js", "type": "file", "size": 2536, "mtime": 1753079572000}, {"name": "createCorpusNote.js", "type": "file", "size": 15514, "mtime": 1754166938000}, {"name": "createPracticeNote.js", "type": "file", "size": 5354, "mtime": 1754094170000}, {"name": "customAI.js", "type": "file", "size": 7896, "mtime": 1753960219000}, {"name": "extractCorpus.js", "type": "file", "size": 13443, "mtime": 1754191935000}, {"name": "extractHeadingContent.js", "type": "file", "size": 2854, "mtime": 1753067289000}, {"name": "generateSpecificScenario.js", "type": "file", "size": 18305, "mtime": 1754084424000}, {"name": "insertTaskToProject.js", "type": "file", "size": 1569, "mtime": 1753067290000}, {"name": "insertTaskToProject2.0.js", "type": "file", "size": 2603, "mtime": 1753067290000}, {"name": "insertWeek.js", "type": "file", "size": 3867, "mtime": 1753067290000}, {"name": "keywordFlowchartGenerator.js", "type": "file", "size": 7746, "mtime": 1753860680000}, {"name": "originalIntentSimple.js", "type": "file", "size": 19867, "mtime": 1754192293000}, {"name": "practiceEvaluation.js", "type": "file", "size": 9184, "mtime": 1754095339000}, {"name": "quickAI2.js", "type": "file", "size": 13050, "mtime": 1754042241000}, {"name": "quickAI_MultiAPI.js", "type": "file", "size": 13025, "mtime": 1754074879000}, {"name": "sleepCalculator-1.0.js", "type": "file", "size": 2297, "mtime": 1753067290000}, {"name": "smartHeadingExtractor.js", "type": "file", "size": 3435, "mtime": 1753067290000}, {"name": "switchLightDark.js", "type": "file", "size": 504, "mtime": 1731453377000}, {"name": "updatePracticeDate.js", "type": "file", "size": 1754, "mtime": 1754047767000}, {"name": "xiaohongshu.js", "type": "file", "size": 5406, "mtime": 1753934179000}]}]}, {"name": "闪卡", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754305061000, "type": "directory", "children": [{"name": "政策文件翻译", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754306160000, "type": "directory", "children": [{"name": "政策文件学习笔记", "type": "directory", "size": 0, "mtime": 1754306162000, "children": [{"name": "「中英官方法律文本对照差异」.md", "type": "file", "size": 5029, "mtime": 1753993295000}]}, {"name": "政策原文件", "type": "directory", "size": 0, "mtime": 1754306161000, "children": [{"name": "《中华人民共和国个人信息保护法》.md", "type": "file", "size": 73360, "mtime": 1753992599000}]}, {"name": "政策翻译切分", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754308696303, "type": "directory", "children": [{"name": "《中华人民共和国个人信息保护法》发布与生效日期.md", "type": "file", "size": 1000, "mtime": 1753992904000}, {"name": "个人信息处理目的与范围限制.md", "type": "file", "size": 810, "mtime": 1753992904000}, {"name": "个人信息处理原则.md", "type": "file", "size": 774, "mtime": 1753992904000}, {"name": "个人信息处理者权利与义务.md", "type": "file", "size": 813, "mtime": 1753992904000}, {"name": "个人信息处理者义务与责任.md", "type": "file", "size": 849, "mtime": 1753992904000}, {"name": "个人信息处理者与接收方关系.md", "type": "file", "size": 792, "mtime": 1753992904000}, {"name": "个人信息处理者与受托人关系.md", "type": "file", "size": 828, "mtime": 1753992904000}, {"name": "个人信息定义与处理范围.md", "type": "file", "size": 852, "mtime": 1753992904000}, {"name": "个人信息跨境提供规则.md", "type": "file", "size": 912, "mtime": 1753992904000}]}]}, {"name": "主干法", "type": "directory", "size": 0, "mtime": 1754305061000, "children": [{"name": "Access is restricted to ___ personnel.md", "type": "file", "size": 1603, "mtime": 1753124966000}, {"name": "All affected parties must be notified within ___ hours.md", "type": "file", "size": 1738, "mtime": 1753774871000}, {"name": "This falls under the scope of ___.md", "type": "file", "size": 1498, "mtime": 1753124925000}, {"name": "User data is ___ for [specific] purposes only.md", "type": "file", "size": 1629, "mtime": 1753124924000}, {"name": "We have detected a potential ___ incident.md", "type": "file", "size": 1706, "mtime": 1753774978000}, {"name": "[Data] is encrypted ___.md", "type": "file", "size": 1568, "mtime": 1753774730000}, {"name": "[Entity] shall implement ___ to ensure ___.md", "type": "file", "size": 1737, "mtime": 1753125055000}, {"name": "[Organization] must obtain [explicit-prior] consent before ___.md", "type": "file", "size": 1692, "mtime": 1753802303000}, {"name": "[System] is designed to be ___ by default.md", "type": "file", "size": 1724, "mtime": 1753775027000}, {"name": "[动作] shall be [状态] provided that [条件].md", "type": "file", "size": 1534, "mtime": 1753771700000}, {"name": "[数据] may be transferred to [国家] where [保障].md", "type": "file", "size": 1839, "mtime": 1753772652000}, {"name": "[数据主体] has the right to [权利].md", "type": "file", "size": 1635, "mtime": 1753125426000}, {"name": "[行为] is prohibited unless [例外].md", "type": "file", "size": 1419, "mtime": 1753124191000}, {"name": "[要求] pursuant to [法律] Article [条款].md", "type": "file", "size": 1388, "mtime": 1753774811000}, {"name": "[用户] has the right to request [动作] [时限].md", "type": "file", "size": 1489, "mtime": 1753124332000}, {"name": "[用户] has the right to [动作] [时限].md", "type": "file", "size": 1367, "mtime": 1753124121000}, {"name": "[主体] may [动作] where [条件].md", "type": "file", "size": 1495, "mtime": 1753125101000}, {"name": "[主体] must be [动作] [条件].md", "type": "file", "size": 1529, "mtime": 1753124868000}, {"name": "[主体] must document [条款] [条件].md", "type": "file", "size": 1594, "mtime": 1753124352000}, {"name": "[主体] shall conduct [评估类型] to assess [风险] [条件].md", "type": "file", "size": 2065, "mtime": 1753124705000}, {"name": "[主体] shall ensure [数据] is [状态] [条件].md", "type": "file", "size": 1813, "mtime": 1753773731000}, {"name": "[主体] shall implement [措施] to ensure [结果].md", "type": "file", "size": 1698, "mtime": 1753124371000}, {"name": "[主体] shall notify [对象] within [时限] upon [事件].md", "type": "file", "size": 1933, "mtime": 1753125107000}, {"name": "[主体] shall obtain [同意类型] consent from [对象] before [动作].md", "type": "file", "size": 1790, "mtime": 1753124921000}, {"name": "[主体] shall [动作] [条款].md", "type": "file", "size": 1450, "mtime": 1753124868000}, {"name": "看到欧盟法规预期shall.md", "type": "file", "size": 1525, "mtime": 1753174702000}, {"name": "强调动作对象[技术措施] must be.md", "type": "file", "size": 1437, "mtime": 1753174702000}, {"name": "强调前提条件is required where [跨境传输].md", "type": "file", "size": 1568, "mtime": 1753174702000}, {"name": "强调责任主体[数据控制者] shall.md", "type": "file", "size": 1443, "mtime": 1753956772000}, {"name": "数据状态描述.md", "type": "file", "size": 892, "mtime": 1753774436000}, {"name": "主干法.md", "type": "file", "size": 14564, "mtime": 1753174821000}]}, {"name": "短语", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754308696302, "type": "directory", "children": [{"name": "24-7.md", "type": "file", "size": 1031, "mtime": 1753174821000}, {"name": "at least.md", "type": "file", "size": 1112, "mtime": 1753126375000}, {"name": "based on.md", "type": "file", "size": 1117, "mtime": 1753127348000}, {"name": "bi-monthly.md", "type": "file", "size": 1144, "mtime": 1753127176000}, {"name": "business hours.md", "type": "file", "size": 1241, "mtime": 1753126971000}, {"name": "by means of.md", "type": "file", "size": 1247, "mtime": 1753126210000}, {"name": "comprehensively.md", "type": "file", "size": 1176, "mtime": 1753126700000}, {"name": "data breach.md", "type": "file", "size": 1180, "mtime": 1753127256000}, {"name": "every 24 hours.md", "type": "file", "size": 1099, "mtime": 1753128233000}, {"name": "failover.md", "type": "file", "size": 1178, "mtime": 1753127289000}, {"name": "from time to time.md", "type": "file", "size": 1178, "mtime": 1753126570000}, {"name": "immediately after.md", "type": "file", "size": 1246, "mtime": 1753126376000}, {"name": "in accordance with.md", "type": "file", "size": 1290, "mtime": 1753126376000}, {"name": "in case of.md", "type": "file", "size": 1190, "mtime": 1753126774000}, {"name": "in respect of.md", "type": "file", "size": 1257, "mtime": 1753126393000}, {"name": "in the event of.md", "type": "file", "size": 1258, "mtime": ***********00}, {"name": "in virtual data centers.md", "type": "file", "size": 1426, "mtime": 1753126344000}, {"name": "no later than 72 hours.md", "type": "file", "size": 1290, "mtime": 1753126947000}, {"name": "no more than.md", "type": "file", "size": 1189, "mtime": 1753126629000}, {"name": "off-peak hours.md", "type": "file", "size": 1276, "mtime": 1753126991000}, {"name": "prior to implementation.md", "type": "file", "size": 1295, "mtime": 1753126377000}, {"name": "proactively.md", "type": "file", "size": 1166, "mtime": 1753126741000}, {"name": "quarterly.md", "type": "file", "size": 1153, "mtime": 1753126905000}, {"name": "real-time.md", "type": "file", "size": 1112, "mtime": 1753127088000}, {"name": "regularly.md", "type": "file", "size": 1127, "mtime": 1753126680000}, {"name": "restore from backup.md", "type": "file", "size": 1303, "mtime": 1753126822000}, {"name": "revoke access.md", "type": "file", "size": 1282, "mtime": 1754299397000}, {"name": "securely.md", "type": "file", "size": 1103, "mtime": 1753126652000}, {"name": "semi-annual.md", "type": "file", "size": 1140, "mtime": 1753127210000}, {"name": "service continuity.md", "type": "file", "size": 1332, "mtime": 1753127270000}, {"name": "subject to.md", "type": "file", "size": 1246, "mtime": 1753126668000}, {"name": "thoroughly examine.md", "type": "file", "size": 1363, "mtime": 1753126319000}, {"name": "to ensure service continuity.md", "type": "file", "size": 1514, "mtime": 1753126363000}, {"name": "twice weekly.md", "type": "file", "size": 1098, "mtime": 1754299406000}, {"name": "unauthorized access.md", "type": "file", "size": 1257, "mtime": 1753296257000}, {"name": "upon termination.md", "type": "file", "size": 1205, "mtime": 1754299409000}, {"name": "with regard to.md", "type": "file", "size": 1266, "mtime": 1753127370000}, {"name": "within 24 hours.md", "type": "file", "size": 1171, "mtime": 1754299411000}, {"name": "within the financial year.md", "type": "file", "size": 1326, "mtime": 1753126591000}]}, {"name": "合规", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754308696302, "type": "directory", "children": [{"name": "72⼩时内上报.md", "type": "file", "size": 1034, "mtime": 1753147910000}, {"name": "⾃动化决策必须允许申诉.md", "type": "file", "size": 1118, "mtime": 1753147910000}, {"name": "⾃动化决策需要定期审查.md", "type": "file", "size": 1100, "mtime": 1753147910000}, {"name": "⽤户同意⾄上.md", "type": "file", "size": 1060, "mtime": 1753147910000}, {"name": "⽤户同意必须记录.md", "type": "file", "size": 1030, "mtime": 1753801851000}, {"name": "⼉童数据需要家⻓同意.md", "type": "file", "size": 479, "mtime": 1753802074000}, {"name": "⽣物识别数据是敏感的.md", "type": "file", "size": 1092, "mtime": 1753147910000}, {"name": "⽇志个⼈数据.md", "type": "file", "size": 1044, "mtime": 1753147910000}, {"name": "AI中的伦理考量.md", "type": "file", "size": 1086, "mtime": 1753147910000}, {"name": "AI中的数据最⼩化.md", "type": "file", "size": 1096, "mtime": 1753147910000}, {"name": "DPO必须直接向最⾼管理层汇报.md", "type": "file", "size": 1180, "mtime": 1753147910000}, {"name": "被遗忘权.md", "type": "file", "size": 1038, "mtime": 1753147910000}, {"name": "必须明确获得同意.md", "type": "file", "size": 1088, "mtime": 1753147910000}, {"name": "必须匿名化.md", "type": "file", "size": 1048, "mtime": 1753147910000}, {"name": "必须设⽴数据保护官 (DPO).md", "type": "file", "size": 1134, "mtime": 1753147910000}, {"name": "必须尊重⽤户权利.md", "type": "file", "size": 1062, "mtime": 1753147910000}, {"name": "此条款产⽣共同控制者责任.md", "type": "file", "size": 1106, "mtime": 1753147910000}, {"name": "定期第三⽅审计.md", "type": "file", "size": 1100, "mtime": 1753147910000}, {"name": "定期合规检查.md", "type": "file", "size": 1056, "mtime": 1753147910000}, {"name": "定期合规审查.md", "type": "file", "size": 1084, "mtime": 1753147910000}, {"name": "定期合规审查2.md", "type": "file", "size": 1086, "mtime": 1753147910000}, {"name": "定期漏洞评估.md", "type": "file", "size": 1070, "mtime": 1753147910000}, {"name": "定期培训.md", "type": "file", "size": 1060, "mtime": 1753147910000}, {"name": "定期审计.md", "type": "file", "size": 1052, "mtime": 1753147910000}, {"name": "定期数据分类.md", "type": "file", "size": 1064, "mtime": 1753147910000}, {"name": "定期数据伦理培训.md", "type": "file", "size": 1078, "mtime": 1753147910000}, {"name": "定期数据匿名化检查.md", "type": "file", "size": 1108, "mtime": 1753147910000}, {"name": "定期数据审计.md", "type": "file", "size": 1068, "mtime": 1753147910000}, {"name": "定期数据审计2.md", "type": "file", "size": 1070, "mtime": 1753147910000}, {"name": "定期数据映射.md", "type": "file", "size": 1092, "mtime": 1753147910000}, {"name": "罚款.md", "type": "file", "size": 964, "mtime": 1753147910000}, {"name": "访问控制.md", "type": "file", "size": 1030, "mtime": 1753147910000}, {"name": "个⼈数据的伦理使⽤.md", "type": "file", "size": 1072, "mtime": 1753147910000}, {"name": "根据第66(1)条9 需⽴即暂停.md", "type": "file", "size": 1114, "mtime": 1753147910000}, {"name": "合规.txt", "type": "file", "size": 60806, "mtime": 1753124380000}, {"name": "加密是强制性的.md", "type": "file", "size": 1068, "mtime": 1753147910000}, {"name": "假名化匿名化.md", "type": "file", "size": 1086, "mtime": 1753147910000}, {"name": "监管机构问询.md", "type": "file", "size": 1012, "mtime": 1753147910000}, {"name": "仅匿名化还不够.md", "type": "file", "size": 1096, "mtime": 1753147910000}, {"name": "紧急灭⽕.md", "type": "file", "size": 988, "mtime": 1753147910000}, {"name": "禁⽌出售数据.md", "type": "file", "size": 1052, "mtime": 1753147910000}, {"name": "禁⽌第三⽅共享.md", "type": "file", "size": 1088, "mtime": 1753147910000}, {"name": "联合责任.md", "type": "file", "size": 988, "mtime": 1753147910000}, {"name": "伦理AI.md", "type": "file", "size": 1034, "mtime": 1753147910000}, {"name": "伦理AI框架.md", "type": "file", "size": 1050, "mtime": 1753147910000}, {"name": "默认隐私保护.md", "type": "file", "size": 1068, "mtime": 1753147910000}, {"name": "你的API⽇志属于个⼈数据.md", "type": "file", "size": 1132, "mtime": 1753147910000}, {"name": "你要求的保留期限违反了⽐例原则.md", "type": "file", "size": 1176, "mtime": 1753147910000}, {"name": "匿名化与假名化的区别.md", "type": "file", "size": 1138, "mtime": 1753147910000}, {"name": "欧盟数据保护委员会意⻅.md", "type": "file", "size": 1060, "mtime": 1753147910000}, {"name": "确认涉及欧盟数据的泄露.md", "type": "file", "size": 1090, "mtime": 1753147910000}, {"name": "声誉⻛险.md", "type": "file", "size": 1018, "mtime": 1753147910000}, {"name": "数据保护影响评估 (DPIA).md", "type": "file", "size": 1112, "mtime": 1753147910000}, {"name": "数据保护影响评估的缓解措施已逾期14天.md", "type": "file", "size": 1150, "mtime": 1753147910000}, {"name": "数据保留期限.md", "type": "file", "size": 1040, "mtime": 1753147910000}, {"name": "数据保留与删除的平衡.md", "type": "file", "size": 1112, "mtime": 1753147910000}, {"name": "数据保留政策.md", "type": "file", "size": 1062, "mtime": 1753147910000}, {"name": "数据本地化要求.md", "type": "file", "size": 1038, "mtime": 1753147910000}, {"name": "数据可携带权.md", "type": "file", "size": 1058, "mtime": 1753147910000}, {"name": "数据伦理框架.md", "type": "file", "size": 1084, "mtime": 1753147910000}, {"name": "数据伦理培训.md", "type": "file", "size": 1086, "mtime": 1753147910000}, {"name": "数据泄露应对计划.md", "type": "file", "size": 1080, "mtime": 1753147910000}, {"name": "数据泄露应对计划2.md", "type": "file", "size": 1082, "mtime": 1753147910000}, {"name": "数据泄露应对协议.md", "type": "file", "size": 1074, "mtime": 1753147910000}, {"name": "数据主权.md", "type": "file", "size": 1064, "mtime": 1753147910000}, {"name": "数据主体请求.md", "type": "file", "size": 1060, "mtime": 1753147910000}, {"name": "数据最⼩化.md", "type": "file", "size": 1050, "mtime": 1753147910000}, {"name": "透明原则.md", "type": "file", "size": 1052, "mtime": 1753147910000}, {"name": "未经同意禁⽌画像.md", "type": "file", "size": 1088, "mtime": 1753147910000}, {"name": "未经同意禁⽌数据挖掘.md", "type": "file", "size": 1056, "mtime": 1753801851000}, {"name": "我们维护详细的处理活动记录.md", "type": "file", "size": 1118, "mtime": 1753147910000}, {"name": "我们已启动约束性公司规则的咨询.md", "type": "file", "size": 1146, "mtime": 1753147910000}, {"name": "我们在施雷姆斯⼆号案的观察名单上.md", "type": "file", "size": 1106, "mtime": 1753147910000}, {"name": "已咨询主导监管机构.md", "type": "file", "size": 1068, "mtime": 1753147910000}, {"name": "引⽤C-31118案.md", "type": "file", "size": 1026, "mtime": 1753147910000}, {"name": "隐私设计.md", "type": "file", "size": 1054, "mtime": 1753147910000}, {"name": "隐私设计2.md", "type": "file", "size": 1056, "mtime": 1753147910000}, {"name": "影响评估超期.md", "type": "file", "size": 1074, "mtime": 1753147910000}, {"name": "与董事会⾼层的讨论 - DPO⻆⾊.md", "type": "file", "size": 1096, "mtime": 1753147910000}, {"name": "与董事会⾼层的讨论 - 定期审计.md", "type": "file", "size": 1108, "mtime": 1753147910000}, {"name": "与董事会⾼层的讨论 - 内部管理.md", "type": "file", "size": 1108, "mtime": 1753147910000}, {"name": "与董事会⾼层的讨论 - 预算与合规.md", "type": "file", "size": 1120, "mtime": 1753147910000}, {"name": "与法律顾问的讨论 - ⽤户同意.md", "type": "file", "size": 1048, "mtime": 1753801851000}, {"name": "与法律顾问的讨论 - 法律咨询.md", "type": "file", "size": 1160, "mtime": 1753147910000}, {"name": "与法律顾问的讨论 - 合规审查.md", "type": "file", "size": 1078, "mtime": 1753147910000}, {"name": "与技术团队的讨论 - 加密.md", "type": "file", "size": 1108, "mtime": 1753147910000}, {"name": "与技术团队的讨论 - 假名化与匿名化.md", "type": "file", "size": 1140, "mtime": 1753147910000}, {"name": "与技术团队的讨论 - 数据处理.md", "type": "file", "size": 1096, "mtime": 1753147910000}, {"name": "与技术团队的讨论 - 项⽬上线前.md", "type": "file", "size": 1108, "mtime": 1753147910000}, {"name": "与监管机构的讨论.md", "type": "file", "size": 1036, "mtime": 1753147910000}, {"name": "与客户的讨论.md", "type": "file", "size": 1012, "mtime": 1753147910000}, {"name": "与数据保护官的讨论 - ⽇常沟通.md", "type": "file", "size": 1108, "mtime": 1753147910000}, {"name": "与数据保护官的讨论 - 紧急事件.md", "type": "file", "size": 1108, "mtime": 1753147910000}, {"name": "与数据保护官的讨论 - 培训.md", "type": "file", "size": 1084, "mtime": 1753147910000}, {"name": "这符合EDPB 2021年7⽉的意⻅.md", "type": "file", "size": 1066, "mtime": 1753147910000}, {"name": "这可能触发全球营业额4的罚款.md", "type": "file", "size": 1134, "mtime": 1753147910000}, {"name": "尊重⽤户权利和⾃由.md", "type": "file", "size": 1098, "mtime": 1753147910000}]}, {"name": "场景", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754308696302, "type": "directory", "children": [{"name": "⾼⻛险标准.md", "type": "file", "size": 919, "mtime": 1753148543000}, {"name": "⽂档要求.md", "type": "file", "size": 947, "mtime": 1753147911000}, {"name": "⻛险评估.md", "type": "file", "size": 907, "mtime": 1753148512000}, {"name": "GDPR的域外适⽤性.md", "type": "file", "size": 987, "mtime": 1753147911000}, {"name": "被遗忘权删除权.md", "type": "file", "size": 1117, "mtime": 1753148609000}, {"name": "补救措施.md", "type": "file", "size": 947, "mtime": 1753147911000}, {"name": "测试环境中的数据保护.md", "type": "file", "size": 979, "mtime": 1753148539000}, {"name": "场景.txt", "type": "file", "size": 49324, "mtime": 1753124444000}, {"name": "第三⽅数据访问控制.md", "type": "file", "size": 1007, "mtime": 1753147911000}, {"name": "反对权.md", "type": "file", "size": 947, "mtime": 1753147911000}, {"name": "个⼈通知内容.md", "type": "file", "size": 931, "mtime": 1753148545000}, {"name": "国际数据传输保障措施.md", "type": "file", "size": 979, "mtime": 1753148542000}, {"name": "后续⾏动.md", "type": "file", "size": 907, "mtime": 1753148510000}, {"name": "基于⻆⾊的访问控制 (RBAC).md", "type": "file", "size": 991, "mtime": 1753148639000}, {"name": "监管报告内容.md", "type": "file", "size": 971, "mtime": 1753147911000}, {"name": "就业背景调查中的数据保护.md", "type": "file", "size": 1043, "mtime": 1753147911000}, {"name": "媒体请求的数据保护原则.md", "type": "file", "size": 991, "mtime": 1753148512000}, {"name": "内部安全事件报告.md", "type": "file", "size": 995, "mtime": 1753147911000}, {"name": "受影响⽅通知.md", "type": "file", "size": 971, "mtime": 1753147911000}, {"name": "数据处理活动记录 (ROPA).md", "type": "file", "size": 1023, "mtime": 1753147911000}, {"name": "数据更正权.md", "type": "file", "size": 973, "mtime": 1753147911000}, {"name": "数据可携权.md", "type": "file", "size": 979, "mtime": 1753147911000}, {"name": "数据销售的合规要求.md", "type": "file", "size": 1008, "mtime": 1753148562000}, {"name": "数据泄露通知.md", "type": "file", "size": 1057, "mtime": 1753147911000}, {"name": "数据泄露通知义务.md", "type": "file", "size": 995, "mtime": 1753147911000}, {"name": "物理安全事件处理.md", "type": "file", "size": 995, "mtime": 1753147911000}, {"name": "响应计划要求.md", "type": "file", "size": 931, "mtime": 1753148564000}, {"name": "营销活动中的数据使⽤规则.md", "type": "file", "size": 1043, "mtime": 1753147911000}, {"name": "营销邮件退订和合规.md", "type": "file", "size": 1008, "mtime": 1753148534000}, {"name": "营销中的数据处理和第三⽅共享.md", "type": "file", "size": 1027, "mtime": 1753148529000}, {"name": "主体访问请求 (SAR).md", "type": "file", "size": 1003, "mtime": 1753147911000}]}, {"name": "开会", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754308696302, "type": "directory", "children": [{"name": "⽼外说的 - 表达时间紧迫.md", "type": "file", "size": 1028, "mtime": 1753147911000}, {"name": "⽼外说的 - 表达疑问.md", "type": "file", "size": 1004, "mtime": 1753147911000}, {"name": "⽼外说的 - 表示进展顺利.md", "type": "file", "size": 1028, "mtime": 1753147911000}, {"name": "⽼外说的 - 分享屏幕.md", "type": "file", "size": 964, "mtime": 1753148526000}, {"name": "⽼外说的 - 开始会议.md", "type": "file", "size": 1004, "mtime": 1753147910000}, {"name": "⽼外说的 - 请求更详细的信息.md", "type": "file", "size": 1052, "mtime": 1753147911000}, {"name": "⽼外说的 - 确认⾳频视频.md", "type": "file", "size": 1028, "mtime": 1753147911000}, {"name": "⽼外说的 - 确认任务分配.md", "type": "file", "size": 988, "mtime": 1753148525000}, {"name": "⽼外说的 - 提出建议.md", "type": "file", "size": 1004, "mtime": 1753147911000}, {"name": "⽼外说的 - 提供⽀持.md", "type": "file", "size": 1004, "mtime": 1753147911000}, {"name": "⽼外说的 - 提供反馈.md", "type": "file", "size": 964, "mtime": 1753148524000}, {"name": "⽼外说的 - 提供替代⽅案.md", "type": "file", "size": 1028, "mtime": 1753147911000}, {"name": "⽼外说的 - 邀请发⾔.md", "type": "file", "size": 964, "mtime": 1753148524000}, {"name": "⽼外说的 - 要求对⽅静⾳.md", "type": "file", "size": 1028, "mtime": 1753147911000}, {"name": "⽼外说的 - 征求反馈.md", "type": "file", "size": 964, "mtime": 1753148519000}, {"name": "Can you give me a heads-up.md", "type": "file", "size": 1002, "mtime": 1753147910000}, {"name": "Data_Breach_Notification_Requirements.md", "type": "file", "size": 1146, "mtime": 1753174669000}, {"name": "Data_Subject_Rights_Responses.md", "type": "file", "size": 892, "mtime": 1753174670000}, {"name": "GDPR_Compliance_Emergency_Phrases.md", "type": "file", "size": 1516, "mtime": 1753174671000}, {"name": "GDPR_Contract_Negotiation_Phrases.md", "type": "file", "size": 850, "mtime": 1753174673000}, {"name": "GDPR_Data_Breach_72Hour_Notification.md", "type": "file", "size": 776, "mtime": 1753174674000}, {"name": "GDPR_Data_Breach_72_Hour_Rule.md", "type": "file", "size": 837, "mtime": 1753174672000}, {"name": "GDPR_Data_Breach_Notification.md", "type": "file", "size": 1014, "mtime": 1753174675000}, {"name": "GDPR_Data_Breach_Notification_72_Hour_Rule.md", "type": "file", "size": 987, "mtime": 1753174677000}, {"name": "GDPR_Data_Breach_Notification_Phrases.md", "type": "file", "size": 903, "mtime": 1753174676000}, {"name": "GDPR_Emergency_Phrases.md", "type": "file", "size": 1877, "mtime": 1753174679000}, {"name": "GDPR_Emergency_Response_Phrases.md", "type": "file", "size": 1001, "mtime": 1753174680000}, {"name": "I'll get back to you on that.md", "type": "file", "size": 1010, "mtime": 1753147910000}, {"name": "I'll loop you in.md", "type": "file", "size": 962, "mtime": 1753147910000}, {"name": "I'm just following up on that.md", "type": "file", "size": 1012, "mtime": 1753147910000}, {"name": "in minutes.md", "type": "file", "size": 896, "mtime": 1753148518000}, {"name": "Keep me in the loop.md", "type": "file", "size": 974, "mtime": 1753147910000}, {"name": "Let's circle back to this.md", "type": "file", "size": 996, "mtime": 1753147910000}, {"name": "Let's table that for now.md", "type": "file", "size": 992, "mtime": 1753147910000}, {"name": "Let's touch base later.md", "type": "file", "size": 984, "mtime": 1753147910000}, {"name": "Meeting_Follow_Up_Phrases.md", "type": "file", "size": 1083, "mtime": 1753174682000}, {"name": "Professional_Meeting_Management_Phrases.md", "type": "file", "size": 1676, "mtime": 1753174683000}, {"name": "summarize - 结束总结.md", "type": "file", "size": 1018, "mtime": 1753147910000}, {"name": "summarize - 开会总结.md", "type": "file", "size": 992, "mtime": 1753147910000}, {"name": "summarize - 请求总结.md", "type": "file", "size": 970, "mtime": 1753148520000}, {"name": "summarize - 确认理解.md", "type": "file", "size": 984, "mtime": 1753147910000}, {"name": "summarize - 确认总结的准确性.md", "type": "file", "size": 1072, "mtime": 1753147910000}, {"name": "summarize - 提供总结.md", "type": "file", "size": 952, "mtime": 1753148520000}, {"name": "summarize - 总结并感谢参与者.md", "type": "file", "size": 1118, "mtime": 1753147910000}, {"name": "summarize - 总结并提出下⼀步.md", "type": "file", "size": 1000, "mtime": 1753148521000}, {"name": "summarize - 总结议程项.md", "type": "file", "size": 1004, "mtime": 1753147910000}, {"name": "Technical_Team_GDPR_Discussions.md", "type": "file", "size": 1254, "mtime": 1753174685000}, {"name": "That sounds good to me.md", "type": "file", "size": 946, "mtime": 1753148522000}, {"name": "While we may disagree on specific point, we both agree that common ground..md", "type": "file", "size": 1160, "mtime": 1753148522000}, {"name": "Wrap Up结束 Kick Off开始.md", "type": "file", "size": 1008, "mtime": 1753147910000}, {"name": "表达⾃⼰观点 - 回应争议.md", "type": "file", "size": 966, "mtime": 1753148529000}, {"name": "表达⾃⼰观点 - 结束语.md", "type": "file", "size": 1024, "mtime": 1753174657000}, {"name": "表达⾃⼰观点 - 开放讨论.md", "type": "file", "size": 1028, "mtime": 1753147911000}, {"name": "表达⾃⼰观点 - 明确表达你的观点.md", "type": "file", "size": 1042, "mtime": 1753174396000}, {"name": "表达⾃⼰观点 - 使⽤数据⽀持.md", "type": "file", "size": 1106, "mtime": 1753148514000}, {"name": "禁⽌ ban on.md", "type": "file", "size": 894, "mtime": 1753148531000}, {"name": "开会.txt", "type": "file", "size": 95436, "mtime": 1753124522000}, {"name": "开会流程 - 表达疑问.md", "type": "file", "size": 1012, "mtime": 1753147911000}, {"name": "开会流程 - 分配任务.md", "type": "file", "size": 1014, "mtime": 1753147911000}, {"name": "开会流程 - 后续⾏动.md", "type": "file", "size": 964, "mtime": 1753148515000}, {"name": "开会流程 - 结束会议.md", "type": "file", "size": 1004, "mtime": 1753147911000}, {"name": "开会流程 - 介绍议程.md", "type": "file", "size": 1004, "mtime": 1753147911000}, {"name": "开会流程 - 开始会议.md", "type": "file", "size": 964, "mtime": 1753148526000}, {"name": "开会流程 - 请求发⾔.md", "type": "file", "size": 964, "mtime": 1753148514000}, {"name": "开会流程 - 确认参与者.md", "type": "file", "size": 1064, "mtime": 1753147911000}, {"name": "开会流程 - 确认理解.md", "type": "file", "size": 1050, "mtime": 1753147911000}, {"name": "开会流程 - 提出建议.md", "type": "file", "size": 1070, "mtime": 1753147911000}, {"name": "开会流程 - 提供反馈.md", "type": "file", "size": 1068, "mtime": 1753147911000}, {"name": "开会流程 - 总结.md", "type": "file", "size": 940, "mtime": 1753148516000}, {"name": "使⽤Tenable这个⼯具来进⾏强制性的漏洞评估.md", "type": "file", "size": 1078, "mtime": 1753148517000}, {"name": "数据保护必须符合NIST⽹络安全框架.md", "type": "file", "size": 1228, "mtime": 1753147911000}, {"name": "逐条陈述观点技巧.md", "type": "file", "size": 1062, "mtime": 1753147911000}]}, {"name": "错题本", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754308696302, "type": "directory", "children": [{"name": "according_to_Article_6.md", "type": "file", "size": 1203, "mtime": *************}, {"name": "According_to_authoritative_source_specific_datafinding_which_clearly_demonstrates_that_conclusion.md", "type": "file", "size": 2035, "mtime": *************}, {"name": "According_to_EDPB_Data_Retention_Guidelines_Section_17_we_established_precise_retention_periods_142_days_for_user_behavior_logs_validated_through_Privacy_Impact_Assessment_PIA.md", "type": "file", "size": 1927, "mtime": *************}, {"name": "accountability_under_GDPR_Article_52.md", "type": "file", "size": 1750, "mtime": *************}, {"name": "accuracy_under_GDPR_Article_51d.md", "type": "file", "size": 1750, "mtime": *************}, {"name": "adequacy_decisions.md", "type": "file", "size": 1449, "mtime": *************}, {"name": "adequacy_decision_on_Japan.md", "type": "file", "size": 1259, "mtime": *************}, {"name": "AI_processing_under_GDPR_Articles_22_and_35.md", "type": "file", "size": 1796, "mtime": *************}, {"name": "All_backups_shall_be_encrypted_using_AES-256_per_our_ISMS_Policy_Section_45.md", "type": "file", "size": 1289, "mtime": *************}, {"name": "Although_the_GDPR_imposes_strict_requirements_lawful_data_transfers_can_be_achieved_through_SCCs_Standard_Contractual_Clauses.md", "type": "file", "size": 1413, "mtime": *************}, {"name": "an_encryption_mechanism.md", "type": "file", "size": 960, "mtime": *************}, {"name": "appropriate_technical_and_organisational_measures_under_GDPR_Article_32.md", "type": "file", "size": 2039, "mtime": *************}, {"name": "Article_38_of_PIPL_imposes_unambiguous_data_localization_obligations.md", "type": "file", "size": 1215, "mtime": *************}, {"name": "automated_decision-making_under_GDPR_Article_22.md", "type": "file", "size": 1909, "mtime": 1754299437000}, {"name": "based_on_legitimate_interest.md", "type": "file", "size": 1205, "mtime": *************}, {"name": "Because_cross-border_compliance_is_complex_multinationals_often.md", "type": "file", "size": 1204, "mtime": 1753802406000}, {"name": "binding_corporate_rules_BCRs_under_GDPR_Article_47.md", "type": "file", "size": 1858, "mtime": 1753802406000}, {"name": "biometric_data_processing_under_GDPR_Article_92a_explicit_consent.md", "type": "file", "size": 2072, "mtime": 1753802406000}, {"name": "blockchain_data_processing_under_GDPR_with_immutability_challenges.md", "type": "file", "size": 1939, "mtime": 1753802406000}, {"name": "breach_notification_procedure_under_GDPR_Articles_33_and_34.md", "type": "file", "size": 1866, "mtime": 1754299445000}, {"name": "certification_mechanisms_under_GDPR_Article_42.md", "type": "file", "size": 1736, "mtime": 1754299447000}, {"name": "challenges_on_rainy_days.md", "type": "file", "size": 1074, "mtime": 1753802406000}, {"name": "Chinas_PIPL_Personal_Information_Protection_Law.md", "type": "file", "size": 1722, "mtime": 1753802406000}, {"name": "clinical_research_data_processing_under_GDPR_Article_92j.md", "type": "file", "size": 1988, "mtime": 1753802406000}, {"name": "cloud_processing_arrangement_under_GDPR_Article_28.md", "type": "file", "size": 1874, "mtime": 1754299449000}, {"name": "Cloud_storage_may_be_permissible_provided_it_meets_the_encryption_and_access_control_standards_specified_in_ISOIEC_27018.md", "type": "file", "size": 1533, "mtime": 1753802406000}, {"name": "codes_of_conduct_under_GDPR_Article_40.md", "type": "file", "size": 3244, "mtime": 1753802406000}, {"name": "Colleagues_we_need_to_address_GDPRs_financial_penalties_under_Article_83.md", "type": "file", "size": 1319, "mtime": 1753802406000}, {"name": "collection, storage and processing require consent.md", "type": "file", "size": 3809, "mtime": 1752878534000}, {"name": "Companies_must_comply_with_data_protection_laws.md", "type": "file", "size": 4193, "mtime": 1754299454000}, {"name": "compliance_resilience_stakeholder_trust_operational_efficiency_regulatory_alignment_long-term_data_governance_maturity.md", "type": "file", "size": 3113, "mtime": 1753802406000}, {"name": "comply_with_laws_in_jurisdictions.md", "type": "file", "size": 1170, "mtime": 1753802406000}, {"name": "comply_with_the_law.md", "type": "file", "size": 1187, "mtime": 1753802406000}, {"name": "concerned_supervisory_authority_under_GDPR_Article_422.md", "type": "file", "size": 1785, "mtime": 1753802406000}, {"name": "cookie_consent_under_ePrivacy_Directive_and_GDPR.md", "type": "file", "size": 1822, "mtime": 1753802406000}, {"name": "corporate_restructuring_data_transfer_under_GDPR_Article_61f.md", "type": "file", "size": 1989, "mtime": 1753802406000}, {"name": "Cost-effective_alternatives_such_as_open-source_encryption_tools_and_shared_compliance_platforms_merit_consideration_for_resource-constrained_organizations.md", "type": "file", "size": 1522, "mtime": 1753802406000}, {"name": "Could_you_clarify_the_compliance_requirement_you_mentioned.md", "type": "file", "size": 1147, "mtime": 1754299460000}, {"name": "cross-border_processing_under_GDPR_Article_423.md", "type": "file", "size": 1829, "mtime": 1753802406000}, {"name": "Cross-border_steady-state_data_flows_daily_processing_volume_23TB_have_obtained_CAC_security_assessment_approval.md", "type": "file", "size": 1695, "mtime": 1753802406000}, {"name": "cross-border_transfers.md", "type": "file", "size": 2168, "mtime": 1754299462000}, {"name": "Current_third-party_vendor_monitoring_coverage_92_falls_short_of_ideal_targets_but_dynamic_due_diligence_templates_have_compressed_GDPR_Article_28_risk_exposure_to_below_07.md", "type": "file", "size": 2093, "mtime": 1754299466000}, {"name": "cybersecurity_infrastructure_upgrades.md", "type": "file", "size": 1089, "mtime": 1753802406000}, {"name": "Data_controllers_may_be_required_to_appoint_a_DPO_if_processing_meets_GDPR_Article_37_thresholds.md", "type": "file", "size": 1380, "mtime": 1753184442000}, {"name": "data_controller_and_data_processor_under_GDPR_Article_47_and_48.md", "type": "file", "size": 3039, "mtime": 1753175699000}, {"name": "Data_flows_across_borders_require_adequacy_decisions.md", "type": "file", "size": 1174, "mtime": 1753802406000}, {"name": "data_flows_from_the_EU_to_the_US.md", "type": "file", "size": 1330, "mtime": 1753802406000}, {"name": "data_governance_frameworks.md", "type": "file", "size": 2143, "mtime": 1753802406000}, {"name": "Data_in_transit_must_be_protected_via_TLS_12_or_IPsec_as_per_NIST_SP_800-52B.md", "type": "file", "size": 1262, "mtime": 1753147037000}, {"name": "Data_localization_storage_rate_984_exceeds_the_95_benchmark_required_by_Article_5_of_the_Data_Export_Security_Assessment_Measures.md", "type": "file", "size": 2564, "mtime": 1753147034000}, {"name": "data_minimization_principle_under_GDPR_Article_51c.md", "type": "file", "size": 1902, "mtime": 1753802406000}, {"name": "data_must_be_encrypted_in_transit_and_at_rest.md", "type": "file", "size": 1241, "mtime": 1754299472000}, {"name": "data_processing_agreement_under_GDPR_Article_28.md", "type": "file", "size": 1797, "mtime": 1753802406000}, {"name": "data_processing_records.md", "type": "file", "size": 1488, "mtime": 1753802406000}, {"name": "data_processing_through_our_systems.md", "type": "file", "size": 1287, "mtime": 1753802406000}, {"name": "data_protection_impact_assessment_DPIA_under_GDPR_Article_35.md", "type": "file", "size": 1894, "mtime": 1753802406000}, {"name": "data_protection_officer_DPO_under_GDPR_Article_37.md", "type": "file", "size": 1736, "mtime": 1754299480000}, {"name": "data_retention_periods_under_GDPR_Article_51e.md", "type": "file", "size": 1815, "mtime": 1753802406000}, {"name": "data_subject_access_request_SAR_under_GDPR_Article_15.md", "type": "file", "size": 1780, "mtime": 1754299482000}, {"name": "data_subject_under_GDPR_Article_41.md", "type": "file", "size": 1701, "mtime": 1753802406000}, {"name": "direct_marketing_consent_under_GDPR_Article_212_and_ePrivacy_Directive.md", "type": "file", "size": 1928, "mtime": 1754299484000}, {"name": "DPO_is_responsible_for_monitoring_compliance.md", "type": "file", "size": 1238, "mtime": 1753802406000}, {"name": "educational_data_processing_under_GDPR_Article_61e_public_task.md", "type": "file", "size": 2010, "mtime": 1753802406000}, {"name": "Encryption_is_mandated_under_Article_321a_of_the_GDPR_for_mitigating_processing_risks.md", "type": "file", "size": 1341, "mtime": 1754299490000}, {"name": "encryption_with_AES-256.md", "type": "file", "size": 1186, "mtime": 1753802406000}, {"name": "end-to-end_encryption_protocols.md", "type": "file", "size": 1025, "mtime": 1753802406000}, {"name": "enterprise_under_GDPR_Article_418.md", "type": "file", "size": 1661, "mtime": 1753802406000}, {"name": "explicit_consent_under_GDPR_Article_92a.md", "type": "file", "size": 1804, "mtime": 1753802406000}, {"name": "Failure_to_implement_appropriate_technical_measures_may_result_in_regulatory_sanctions_under_Article_83.md", "type": "file", "size": 1409, "mtime": 1753802406000}, {"name": "filing_system_under_GDPR_Article_46.md", "type": "file", "size": 1743, "mtime": 1753066007000}, {"name": "financial_data_processing_under_GDPR_Article_61c_legal_obligation.md", "type": "file", "size": 1966, "mtime": 1753802406000}, {"name": "framework_mitigation_resilience.md", "type": "file", "size": 3447, "mtime": 1753802406000}, {"name": "Furthermore_Moreover_Additionally_Consequently_Nevertheless.md", "type": "file", "size": 2347, "mtime": 1753802406000}, {"name": "GDPRs_strict_requirements_parallel_the_need_for_rigorous_safeguards_much_like_waterproofing_in_heavy_rain.md", "type": "file", "size": 1395, "mtime": 1753802406000}, {"name": "Given_the_cost-prohibitive_nature_of_full_compliance_a_phased_implementation_approach_is_recommended.md", "type": "file", "size": 1276, "mtime": 1753802406000}, {"name": "individual_data_rights_management.md", "type": "file", "size": 1057, "mtime": 1753802406000}, {"name": "integrity_and_confidentiality_under_GDPR_Article_51f.md", "type": "file", "size": 2017, "mtime": 1753802406000}, {"name": "international_organisation_under_GDPR_Article_426.md", "type": "file", "size": 1828, "mtime": 1753802406000}, {"name": "In_conclusion_the_evidence_overwhelmingly_supports_position_The_implications_of_this_decision_extend_far_beyond_todays_discussion_affecting_broader_impact_I_urge_you_to_specific_action.md", "type": "file", "size": 2397, "mtime": 1753802406000}, {"name": "IoT_data_processing_under_GDPR_with_privacy_by_design.md", "type": "file", "size": 1863, "mtime": 1753802406000}, {"name": "It_is_imperative_to.md", "type": "file", "size": 1163, "mtime": 1753802406000}, {"name": "joint_controllers_under_GDPR_Article_26.md", "type": "file", "size": 1846, "mtime": 1753802406000}, {"name": "joint_controller_arrangement_under_GDPR_Article_26.md", "type": "file", "size": 1873, "mtime": 1753802406000}, {"name": "lawfulness_fairness_and_transparency_under_GDPR_Article_51a.md", "type": "file", "size": 1957, "mtime": 1753802406000}, {"name": "lawful_basis_for_processing_under_GDPR_Article_6.md", "type": "file", "size": 1877, "mtime": 1754299510000}, {"name": "laws of jurisdictions.md", "type": "file", "size": 943, "mtime": 1752878415000}, {"name": "lead_supervisory_authority_under_GDPR_Article_421.md", "type": "file", "size": 1785, "mtime": 1753802406000}, {"name": "legacy_systems.md", "type": "file", "size": 1287, "mtime": 1752877090000}, {"name": "legitimate_interests_assessment_under_GDPR_Article_61f.md", "type": "file", "size": 1910, "mtime": 1753802406000}, {"name": "main_establishment_under_GDPR_Article_416.md", "type": "file", "size": 1753, "mtime": 1753802406000}, {"name": "Minor_breaches_shall_still_be_recorded_per_Article_335.md", "type": "file", "size": 1241, "mtime": 1753802406000}, {"name": "mobile_application_data_processing_under_GDPR_with_privacy_by_design.md", "type": "file", "size": 1889, "mtime": 1754299516000}, {"name": "modular_architecture.md", "type": "file", "size": 1392, "mtime": 1753802406000}, {"name": "Multinational_corporations_must_comply_with_local_data_protection_laws.md", "type": "file", "size": 1296, "mtime": 1754299519000}, {"name": "Not_currently_but_were_finalizing_SCCs_with_Module_Three_for_processor-to-subprocessor_transfers.md", "type": "file", "size": 1365, "mtime": 1753802406000}, {"name": "one-stop-shop_mechanism_under_GDPR_Article_56.md", "type": "file", "size": 1842, "mtime": 1754299522000}, {"name": "Our_AWS_S3_buckets_enforce_server-side_encryption_with_KMS_keys.md", "type": "file", "size": 1252, "mtime": 1753802406000}, {"name": "Our_current_processing_appears_non-compliant_with_GDPRs_lawful_basis_requirements_under_Article_6.md", "type": "file", "size": 1459, "mtime": 1753802406000}, {"name": "Our_Data_Protection_Officer_DPO_system_has_maintained_36_months_of_continuous_compliance_tenure_passing_ISO_27701_annual_surveillance_audits.md", "type": "file", "size": 1804, "mtime": 1753802406000}, {"name": "personal_data_breach_under_GDPR_Article_412.md", "type": "file", "size": 1878, "mtime": 1753802406000}, {"name": "personal_data_under_GDPR_Article_41.md", "type": "file", "size": 1851, "mtime": 1753802406000}, {"name": "privacy-by-design_data_governance_framework_risk-based_approach_cross-functional_collaboration_remediation_plan.md", "type": "file", "size": 3053, "mtime": 1753802406000}, {"name": "privacy_by_design_and_by_default_under_GDPR_Article_25.md", "type": "file", "size": 1840, "mtime": 1753802407000}, {"name": "Privacy_by_Design_principles.md", "type": "file", "size": 2075, "mtime": 1753802406000}, {"name": "privacy_notice_under_GDPR_Articles_13_and_14.md", "type": "file", "size": 1761, "mtime": 1753802406000}, {"name": "Proactive_compliance_monitoring_is_essential_to_mitigate_enforcement_risks_under_Article_834.md", "type": "file", "size": 1392, "mtime": 1753802406000}, {"name": "processing_under_GDPR_Article_42.md", "type": "file", "size": 1762, "mtime": 1753802406000}, {"name": "processor_due_diligence_under_GDPR_Article_28.md", "type": "file", "size": 1763, "mtime": 1753802406000}, {"name": "process_data_based_on_contractual_necessity.md", "type": "file", "size": 1234, "mtime": 1753802406000}, {"name": "profiling_activities.md", "type": "file", "size": 1807, "mtime": 1753802406000}, {"name": "profiling_under_GDPR_Article_44.md", "type": "file", "size": 1861, "mtime": 1753802406000}, {"name": "protect_data_against_unauthorized_access.md", "type": "file", "size": 1302, "mtime": 1753802406000}, {"name": "pseudonymisation_under_GDPR_Article_45_and_anonymisation_techniques.md", "type": "file", "size": 2117, "mtime": 1753802406000}, {"name": "purpose_limitation_under_GDPR_Article_51b.md", "type": "file", "size": 1806, "mtime": 1753802406000}, {"name": "Pursuant_to_Article.md", "type": "file", "size": 1163, "mtime": 1753802406000}, {"name": "recipient_under_GDPR_Article_49.md", "type": "file", "size": 1750, "mtime": 1753802406000}, {"name": "records_of_processing_activities.md", "type": "file", "size": 1779, "mtime": 1753066057000}, {"name": "records_of_processing_activities_under_GDPR_Article_30.md", "type": "file", "size": 1801, "mtime": 1753184370000}, {"name": "Reducing_costs_or_updating_systems_requires_prioritization.md", "type": "file", "size": 1259, "mtime": 1753802406000}, {"name": "regulatory_ambiguity_data_silos_non-compliance_risks_jurisdictional_conflicts.md", "type": "file", "size": 2427, "mtime": 1753802406000}, {"name": "Regulatory_fragmentation_across_jurisdictions_creates_complex_compliance_obligations_particularly_for_data_processors_operating_globally.md", "type": "file", "size": 1516, "mtime": 1753802406000}, {"name": "remove_duplicates.py", "type": "file", "size": 1822, "mtime": 1753169295000}, {"name": "report_breaches_within_72_hours.md", "type": "file", "size": 1248, "mtime": 1754103625000}, {"name": "representative_designation.md", "type": "file", "size": 1654, "mtime": 1753802406000}, {"name": "representative_under_GDPR_Article_27.md", "type": "file", "size": 1707, "mtime": 1753802406000}, {"name": "request_to_delete_data.md", "type": "file", "size": 1227, "mtime": 1753802406000}, {"name": "respond_to_the_SAR_within_30_days.md", "type": "file", "size": 1222, "mtime": 1753802406000}, {"name": "retention_of_5_years.md", "type": "file", "size": 1201, "mtime": 1753802406000}, {"name": "right_to_data_portability_under_GDPR_Article_20.md", "type": "file", "size": 1892, "mtime": 1753802406000}, {"name": "right_to_erasure_under_GDPR_Article_17.md", "type": "file", "size": 1834, "mtime": 1753802406000}, {"name": "right_to_object_under_GDPR_Article_21.md", "type": "file", "size": 1841, "mtime": 1753802406000}, {"name": "right_to_rectification_under_GDPR_Article_16.md", "type": "file", "size": 1789, "mtime": 1753802406000}, {"name": "right_to_restriction_of_processing_under_GDPR_Article_18.md", "type": "file", "size": 1900, "mtime": 1753802406000}, {"name": "risks_associated_with_processing.md", "type": "file", "size": 1276, "mtime": 1753802406000}, {"name": "small_businesses.md", "type": "file", "size": 970, "mtime": 1753009926000}, {"name": "Small_businesses_often_lack_resources_for_the_encryption_required_by_the_GDPR.md", "type": "file", "size": 1400, "mtime": 1753802406000}, {"name": "SMEs_may_encounter_data_mapping_challenges.md", "type": "file", "size": 1198, "mtime": 1753802406000}, {"name": "social_media_data_processing_under_GDPR_with_user_consent.md", "type": "file", "size": 1933, "mtime": 1753802406000}, {"name": "special_categories_of_personal_data_under_GDPR_Article_9.md", "type": "file", "size": 2029, "mtime": 1753802406000}, {"name": "standard_contractual_clauses_SCCs_under_GDPR_Article_46.md", "type": "file", "size": 1862, "mtime": 1753175651000}, {"name": "storage_limitation_under_GDPR_Article_51e.md", "type": "file", "size": 1810, "mtime": 1753802406000}, {"name": "store_data_on_cloud_servers.md", "type": "file", "size": 1246, "mtime": 1752886732000}, {"name": "supervisory_authority_under_GDPR_Article_51.md", "type": "file", "size": 1741, "mtime": 1753802406000}, {"name": "Team_we_need_to_address_the_GDPR_Article_30_record-keeping_obligations.md", "type": "file", "size": 1236, "mtime": 1753802406000}, {"name": "telehealth_data_processing_under_GDPR_Article_92h_healthcare_provision.md", "type": "file", "size": 2011, "mtime": 1753802406000}, {"name": "the_CCPA_California_Consumer_Privacy_Act.md", "type": "file", "size": 1105, "mtime": 1753802406000}, {"name": "The_deployed_homomorphic_encryption_scheme_FV-NTRU_algorithm_ensures_data_utility_without_visibility_satisfying_Cybersecurity_Law_Article_21_technical_measures_requirements.md", "type": "file", "size": 2044, "mtime": 1753802406000}, {"name": "The_extraterritorial_applicability_of_the_GDPR_has_significantly_impacted_international_data_flows_requiring_organizations_to_reassess_their_data_governance_frameworks.md", "type": "file", "size": 1587, "mtime": 1753802406000}, {"name": "The_GDPRs_DPIA_requirement_imposes_costs_on_this_compliance_activity.md", "type": "file", "size": 1388, "mtime": 1753802406000}, {"name": "the_GDPR_General_Data_Protection_Regulation.md", "type": "file", "size": 1755, "mtime": 1753802407000}, {"name": "The_GDPR_stipulates.md", "type": "file", "size": 1209, "mtime": 1753802406000}, {"name": "The_obligation_to_appoint_a_DPO_arises_under_GDPR_Article_37_when_core_activities_involve_large-scale_monitoring.md", "type": "file", "size": 1432, "mtime": 1753802406000}, {"name": "The_processing_poses_a_probable_high_risk_to_data_subjects_rights_under_WP248_guidelines.md", "type": "file", "size": 1321, "mtime": 1753802406000}, {"name": "third_country_under_GDPR_Article_426.md", "type": "file", "size": 1640, "mtime": 1753802406000}, {"name": "This_constitutes_a_high-risk_processing_activity_requiring_a_DPIA_under_GDPR_Article_35.md", "type": "file", "size": 1353, "mtime": 1753802406000}, {"name": "This_marketing_campaign_collects_only_the_minimal_necessary_dataset_containing_12_fields_satisfying_PIPL_Article_6_proportionality_principle.md", "type": "file", "size": 1833, "mtime": 1753802406000}, {"name": "This_processing_activity_falls_within_the_scope_of_PIPL_Article_13_on_cross-border_transfers.md", "type": "file", "size": 1323, "mtime": 1753802406000}, {"name": "Through_CRM_systems_we_achieved_85_data_subject_profiling_coverage_for_EU_residents_complying_with_GDPR_Article_47_definition_for_precise_compliance_management.md", "type": "file", "size": 1939, "mtime": 1753802406000}, {"name": "Through_proactive_compliance_measures_we_not_only_achieve_GDPR_full_alignment_but_transform_data_compliance_into_market_differentiation_advantage.md", "type": "file", "size": 2478, "mtime": 1753802406000}, {"name": "Today_I_stand_before_you_to_argue_that_position_and_I_will_demonstrate_this_through_three_compelling_arguments_point_1_point_2_and_point_3.md", "type": "file", "size": 2730, "mtime": 1753147063000}, {"name": "Tokenization_using_Format-Preserving_Encryption_FPE_reduces_key_management_overhead_by_37_compared_to_AES-256_as_demonstrated_in_EDPB_Case_062020.md", "type": "file", "size": 2206, "mtime": 1753802406000}, {"name": "transfer_data_to_third_countries.md", "type": "file", "size": 1231, "mtime": 1752879823000}, {"name": "Unambiguous_informed_consent_vs_explicit_informed_consent.md", "type": "file", "size": 2289, "mtime": 1753802406000}, {"name": "Under_GDPR_Article_32_encryption_is_required_where_processing_risks_exist.md", "type": "file", "size": 1272, "mtime": 1753147064000}, {"name": "under_PIPL_in_compliance_with_PIPL.md", "type": "file", "size": 1292, "mtime": 1753802406000}, {"name": "Unlike_multinational_enterprises_with_dedicated_compliance_teams_SMEs_often_struggle_to_allocate_sufficient_resources.md", "type": "file", "size": 1416, "mtime": 1753147064000}, {"name": "video_surveillance_processing_under_GDPR_with_legitimate_interests_assessment.md", "type": "file", "size": 1973, "mtime": 1753802406000}, {"name": "We_audit_data_processors_covered_by_our_SCCs.md", "type": "file", "size": 1215, "mtime": 1753147065000}, {"name": "We_comply_with_GDPR_General_Data_Protection_Regulation.md", "type": "file", "size": 1214, "mtime": 1753802406000}, {"name": "When_personal_data_is_transferred_such_transfers_must_use_TLS_12.md", "type": "file", "size": 1313, "mtime": 1753802406000}, {"name": "While_my_opponent_raises_the_concern_about_opponents_point_this_argument_fails_to_consider_critical_factor_and_moreover_stronger_evidence_actually_supports_our_position.md", "type": "file", "size": 2374, "mtime": 1753802406000}, {"name": "While_the_GDPR_emphasizes_data_subject_rights_the_PIPL_focuses_more_on_data_localization_requirements.md", "type": "file", "size": 1245, "mtime": 1753802406000}, {"name": "withdrawal_of_consent_under_GDPR_Article_73.md", "type": "file", "size": 1741, "mtime": 1753802406000}, {"name": "workplace_surveillance_under_GDPR_with_legitimate_interests_assessment.md", "type": "file", "size": 1968, "mtime": 1753802406000}, {"name": "持续合规+截止期限（完成时+将来时）.md", "type": "file", "size": 343, "mtime": 1753068071000}, {"name": "错题本.md", "type": "file", "size": 131, "mtime": 1753802399000}, {"name": "法律条款+当前义务（现在时+现在完成时）.md", "type": "file", "size": 360, "mtime": 1752880149000}, {"name": "侯佩岑式高阶表达训练体系.md", "type": "file", "size": 2175, "mtime": 1753802406000}, {"name": "客户沟通内部培训监管答辩的专业表述模板.md", "type": "file", "size": 3331, "mtime": 1753802406000}, {"name": "历史事件+持续影响（过去时+现在时）.md", "type": "file", "size": 344, "mtime": 1752879161000}, {"name": "数据合规侯佩岑词汇表-_核弹级术语替换.md", "type": "file", "size": 2121, "mtime": 1753802406000}, {"name": "数据最小化原则_data_minimization_principle_替代_targeted_audience.md", "type": "file", "size": 2082, "mtime": 1753802406000}, {"name": "英语表达错题本 - 知识网络索引.md", "type": "file", "size": 4156, "mtime": 1753802408000}]}, {"name": "句式", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754308696302, "type": "directory", "children": [{"name": "According_to_EDPB_2021_Supplementary_Measures_Guidelines_we_restructured_AWS_cloud_data_flow_topology_therefore_maintaining_EU-US_data_transfer_legality_even_post-Schrems_II.md", "type": "file", "size": 2112, "mtime": 1753332035000}, {"name": "Although_PIPL_Article_38_imposes_strict_cross-border_transfer_requirements_global_business_deployment_remains_achievable_through_combined_SCCs_and_cross-border_certification_mechanisms.md", "type": "file", "size": 2133, "mtime": 1753771482000}, {"name": "As_the_EDPB_2023_Guidelines_emphasize_user_profiling_must_provide_explicit_opt-out_options_to_comply_with_AI_Act_Article_14.md", "type": "file", "size": 1927, "mtime": 1753771480000}, {"name": "Based_on_zero-knowledge_proofs_ZKP_user_identity_verification_evolved_from_plaintext_transmission_to_verifiable_yet_non-reversible_security_levels.md", "type": "file", "size": 2122, "mtime": 1753332110000}, {"name": "Data_controllers_shall_implement_appropriate_technical_measures_eg_AES-256_encryption_pursuant_to_GDPR_Article_321a.md", "type": "file", "size": 1696, "mtime": 1753957679000}, {"name": "Data_subjects_shall_receive_confirmation_of_erasure_within_30_calendar_days_in_accordance_with_GDPR_Article_172.md", "type": "file", "size": 1743, "mtime": 1753771481000}, {"name": "Following_automated_DSAR_response_system_implementation_data_deletion_request_processing_time_decreased_from_14_days_to_27_days_fully_complying_with_CCPA_Section_1798105s_45-day_response_limit.md", "type": "file", "size": 2119, "mtime": 1753332227000}, {"name": "If_enterprises_implement_GDPR_Article_35_DPIA_requirements_then_compliance_risks_for_high-risk_data_processing_will_be_systematically_reduced.md", "type": "file", "size": 2513, "mtime": 1753771480000}, {"name": "Looking_ahead_to_2024_we_recommend_proactively_deploying_federated_learning_frameworks_to_address_the_forthcoming_EU_Data_Governance_Acts_novel_regulations_on_data_altruism.md", "type": "file", "size": 2153, "mtime": 1753771480000}, {"name": "The_primary_compliance_challenge_lies_in_难点_especially_when_场景.md", "type": "file", "size": 1876, "mtime": 1753771480000}, {"name": "To_address_问题_implementing_方案_with_具体措施_would_be_advisable.md", "type": "file", "size": 1928, "mtime": 1753771480000}, {"name": "Transfers_to_third_countries_without_adequacy_decisions_require_SCCs_or_Binding_Corporate_Rules_under_GDPR_Chapter_V.md", "type": "file", "size": 1711, "mtime": 1753332173000}, {"name": "Under_法律名称_主体_mustshall_义务.md", "type": "file", "size": 1570, "mtime": 1753332187000}, {"name": "We_have_deployed_a_data_subject_rights_portal_DSAR_Portal_through_automated_workflows_plus_blockchain_attestation_and_achieved_72-hour_response_for_GDPR_Article_15_access_requests.md", "type": "file", "size": 2497, "mtime": 1753771480000}, {"name": "场景短语映射表.md", "type": "file", "size": 2414, "mtime": 1753332599000}]}, {"name": "关系总结", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754308696303, "type": "directory", "children": [{"name": "GDPR核心原则与实施框架.md", "type": "file", "size": 1957, "mtime": 1753802399000}, {"name": "GDPR认证与行为准则体系.md", "type": "file", "size": 1188, "mtime": 1753184877000}, {"name": "充分性决定与第三国传输机制.md", "type": "file", "size": 1172, "mtime": 1753184904000}, {"name": "技术组织措施实施框架.md", "type": "file", "size": 1307, "mtime": 1753184440000}, {"name": "控制者与处理者.md", "type": "file", "size": 2444, "mtime": 1753956654000}, {"name": "联合控制者责任分配机制.md", "type": "file", "size": 1237, "mtime": 1753956576000}, {"name": "区块链与不可变数据处理挑战.md", "type": "file", "size": 1414, "mtime": 1753184919000}, {"name": "数据保护官DPO职责体系.md", "type": "file", "size": 1316, "mtime": 1753184418000}, {"name": "数据保护影响评估DPIA框架.md", "type": "file", "size": 1896, "mtime": 1753184250000}, {"name": "数据处理者义务框架.md", "type": "file", "size": 1038, "mtime": 1753826575000}, {"name": "数据跨境传输合规路径.md", "type": "file", "size": 984, "mtime": 1753219016000}, {"name": "数据泄露通知与响应机制.md", "type": "file", "size": 1304, "mtime": 1753184359000}, {"name": "数据主体权利行使机制.md", "type": "file", "size": 1900, "mtime": 1753184213000}, {"name": "特殊类别数据处理合规框架.md", "type": "file", "size": 1309, "mtime": 1753184890000}, {"name": "同意机制与撤回权利.md", "type": "file", "size": 1221, "mtime": 1753184428000}, {"name": "隐私设计与默认隐私原则.md", "type": "file", "size": 1224, "mtime": 1753184453000}, {"name": "隐私增强技术PETs应用框架.md", "type": "file", "size": 1715, "mtime": 1753184654000}, {"name": "中国PIPL跨境传输合规体系.md", "type": "file", "size": 1583, "mtime": 1753184635000}, {"name": "自动化决策与用户画像规制.md", "type": "file", "size": 1215, "mtime": 1753184609000}]}, {"name": "变量", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754308696304, "type": "directory", "children": [{"name": "法规条款_Legal_Articles.md", "type": "file", "size": 4979, "mtime": 1753336548000}, {"name": "法律名称_Legal_Names.md", "type": "file", "size": 2852, "mtime": 1753336163000}, {"name": "技术方案_Technical_Solutions.md", "type": "file", "size": 6187, "mtime": 1753336619000}, {"name": "时间_Time_Expressions.md", "type": "file", "size": 7104, "mtime": 1753336557000}, {"name": "条件_Conditions.md", "type": "file", "size": 6955, "mtime": 1753336358000}, {"name": "问题_Compliance_Issues.md", "type": "file", "size": 6338, "mtime": 1753336558000}, {"name": "义务_Legal_Obligations.md", "type": "file", "size": 4907, "mtime": 1753336558000}, {"name": "指导文件_Guidance_Documents.md", "type": "file", "size": 7771, "mtime": 1753336445000}, {"name": "主体_Legal_Subjects.md", "type": "file", "size": 4016, "mtime": 1753336185000}]}, {"name": "单词", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754308696304, "type": "directory", "children": [{"name": "闪卡", "type": "file", "size": 28985, "mtime": 1753218426000}]}]}, {"name": "口语训练", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754304494000, "type": "directory", "children": [{"name": "练习场景", "type": "directory", "size": 0, "mtime": 1754304601000, "children": [{"name": "监管机构沟通.md", "type": "file", "size": 4929, "mtime": 1754050639000}, {"name": "客户数据合规咨询.md", "type": "file", "size": 3659, "mtime": 1754050935000}, {"name": "跨境数据传输谈判.md", "type": "file", "size": 5760, "mtime": 1754050723000}, {"name": "隐私影响评估会议.md", "type": "file", "size": 6869, "mtime": 1754050771000}, {"name": "员工数据保护培训.md", "type": "file", "size": 4065, "mtime": 1754050603000}]}, {"name": "杂七杂八", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754304494000, "type": "directory", "children": [{"name": "动图", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754308696302, "type": "directory", "children": [{"name": "2Reeg02CpUnydJ5GFldgmSYpKJmpdbdg.gif", "type": "file", "size": 241571, "mtime": 1754092043000}, {"name": "GIF-动漫_唯美花瓣飘落_爱给网_aigei_com.gif", "type": "file", "size": 982799, "mtime": 1754088676000}, {"name": "GIF_动漫_流星_爱给网_aigei_com.gif", "type": "file", "size": 3792337, "mtime": 1754088672000}, {"name": "OIP.jpg", "type": "file", "size": 6857, "mtime": 1754091818000}, {"name": "OIP.webp", "type": "file", "size": 4094, "mtime": 1754091815000}, {"name": "XIuu0VpSVOqiJVMODmV8xNCnIYRADpK3.gif", "type": "file", "size": 564651, "mtime": 1754091815000}, {"name": "二次元 美少女 初音未来_爱给网_aigei_com 1.gif", "type": "file", "size": 3444034, "mtime": 1754091815000}, {"name": "二次元 美少女 初音未来_爱给网_aigei_com.gif", "type": "file", "size": 3444034, "mtime": 1754088382000}, {"name": "卡通小猫厨房做饭gif表情包 (7)_爱给网_aigei_com 1.gif", "type": "file", "size": 1045610, "mtime": 1754091815000}, {"name": "卡通小猫厨房做饭gif表情包 (7)_爱给网_aigei_com.gif", "type": "file", "size": 1045610, "mtime": 1754088672000}, {"name": "梦幻卡通背景动图 (29)_爱给网_aigei_com 1.gif", "type": "file", "size": 23483593, "mtime": 1754091815000}, {"name": "梦幻卡通背景动图 (29)_爱给网_aigei_com.gif", "type": "file", "size": 23483593, "mtime": 1754088672000}, {"name": "天空 动漫 女生 彩虹 夜景 GIF_爱给网_aigei_com 1.gif", "type": "file", "size": 4692547, "mtime": 1754091815000}, {"name": "天空 动漫 女生 彩虹 夜景 GIF_爱给网_aigei_com.gif", "type": "file", "size": 4692547, "mtime": 1754088672000}]}, {"name": "a4a9d544d9051fb0cecbf7e6b06a687.jpg", "type": "file", "size": 484795, "mtime": 1753995477000}, {"name": "cf0e7fffa0040882cd8677b6d75a451.jpg", "type": "file", "size": 572049, "mtime": 1753995485000}, {"name": "daec787382755c4c8cbff5dd9a9fc12.jpg", "type": "file", "size": 587195, "mtime": 1753995439000}, {"name": "英语练习插件", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754308696303, "type": "directory", "children": [{"name": "README.md", "type": "file", "size": 2717, "mtime": 1754088372000}, {"name": "按钮测试.html", "type": "file", "size": 11239, "mtime": 1753339158000}, {"name": "按钮修复说明.md", "type": "file", "size": 4745, "mtime": 1754088372000}, {"name": "笔记数据提取器.py", "type": "file", "size": 15363, "mtime": 1753337774000}, {"name": "插件改进说明.md", "type": "file", "size": 5923, "mtime": 1754088372000}, {"name": "练习配置.json", "type": "file", "size": 2593, "mtime": 1753337853000}, {"name": "练习数据.json", "type": "file", "size": 387939, "mtime": 1753337853000}, {"name": "使用演示.md", "type": "file", "size": 5933, "mtime": 1754088372000}, {"name": "英语练习插件 1.html", "type": "file", "size": 36396, "mtime": 1753339577000}, {"name": "英语练习插件.html", "type": "file", "size": 54613, "mtime": 1753339100000}, {"name": "英语练习插件使用说明.md", "type": "file", "size": 6096, "mtime": 1754088372000}]}, {"name": "~tmp74_梦幻卡通背景动图 (29)_爱给网_aigei_com.gif", "type": "file", "size": 0, "mtime": 1754088678000}, {"name": "口语练习notion.md", "type": "file", "size": 4875, "mtime": 1754045450000}]}]}, {"name": "英语", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754304617000, "type": "directory", "children": [{"name": "口语训练", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754308696302, "type": "directory", "children": [{"name": "语料卡", "type": "directory", "size": 0, "mtime": 1754304857000, "children": [{"name": "baseline_expectations.md", "type": "file", "size": 652, "mtime": 1754257560000}, {"name": "baseline_expectations（基本期望）.md", "type": "file", "size": 1166, "mtime": 1754257879000}, {"name": "because_someone_could_attack_it.md", "type": "file", "size": 1207, "mtime": 1754168858000}, {"name": "compliance-driven_environments（合规驱动的环境）.md", "type": "file", "size": 1331, "mtime": 1754257778000}, {"name": "compliance_framework.md", "type": "file", "size": 787, "mtime": 1754166570000}, {"name": "configure配置、设置.md", "type": "file", "size": 597, "mtime": 1754146480000}, {"name": "continuous_monitoring_systems（持续监控系统）.md", "type": "file", "size": 1392, "mtime": 1754257926000}, {"name": "could_lead_to_data_breaches.md", "type": "file", "size": 1155, "mtime": 1754169586000}, {"name": "could_lead_to_non-compliance_with_the_company's_da.md", "type": "file", "size": 1755, "mtime": 1754172019000}, {"name": "critical_issue.md", "type": "file", "size": 691, "mtime": 1754166694000}, {"name": "cutting-edge.md", "type": "file", "size": 637, "mtime": 1754257560000}, {"name": "cutting-edge（前沿的）.md", "type": "file", "size": 1038, "mtime": 1754257933000}, {"name": "cybersecurity_threats.md", "type": "file", "size": 775, "mtime": 1754188507000}, {"name": "daily_engagement_with（日常接触）.md", "type": "file", "size": 1241, "mtime": 1754257821000}, {"name": "data_breach.md", "type": "file", "size": 712, "mtime": 1754166650000}, {"name": "data_breaches.md", "type": "file", "size": 709, "mtime": 1754166558000}, {"name": "data_compliance.md", "type": "file", "size": 780, "mtime": 1754188507000}, {"name": "data_governance_framework.md", "type": "file", "size": 857, "mtime": 1754188508000}, {"name": "desensitizes..._to....md", "type": "file", "size": 661, "mtime": 1754257560000}, {"name": "desensitizes..._to...（使……对……麻木）.md", "type": "file", "size": 1256, "mtime": 1754257911000}, {"name": "diminished_perception.md", "type": "file", "size": 751, "mtime": 1754257560000}, {"name": "diminished_perception（减弱的认知）.md", "type": "file", "size": 1430, "mtime": 1754257794000}, {"name": "diminishing_awareness（削弱认知）.md", "type": "file", "size": 1305, "mtime": 1754258013000}, {"name": "dulling_the_ability_to（钝化……的能力）.md", "type": "file", "size": 987, "mtime": 1754257980000}, {"name": "embedded_in.md", "type": "file", "size": 625, "mtime": 1754257560000}, {"name": "embedded_in（置身于）.md", "type": "file", "size": 1421, "mtime": 1754257772000}, {"name": "emerging_threats（新威胁）.md", "type": "file", "size": 1769, "mtime": 1754258032000}, {"name": "enhance_our_network_security_measures.md", "type": "file", "size": 1544, "mtime": 1754172026000}, {"name": "ensures_functional_adherence（确保功能上的遵守）.md", "type": "file", "size": 1458, "mtime": 1754257988000}, {"name": "enterprise-grade_encryption（企业级加密）.md", "type": "file", "size": 1352, "mtime": 1754257918000}, {"name": "erodes_the_recognition_of.md", "type": "file", "size": 672, "mtime": 1754257560000}, {"name": "erodes_the_recognition_of（削弱对……的认知）.md", "type": "file", "size": 1252, "mtime": 1754257839000}, {"name": "ethical_data_collection.md", "type": "file", "size": 760, "mtime": 1754188508000}, {"name": "fostering_complacency（助长自满）.md", "type": "file", "size": 2010, "mtime": 1754258003000}, {"name": "GDPR_certification（GDPR认证）.md", "type": "file", "size": 1355, "mtime": 1754257855000}, {"name": "governance_frameworks（治理框架）.md", "type": "file", "size": 1275, "mtime": 1754257784000}, {"name": "government_subsidies.md", "type": "file", "size": 787, "mtime": 1754188507000}, {"name": "growing_disenchantment_with.md", "type": "file", "size": 693, "mtime": 1754257560000}, {"name": "growing_disenchantment_with（对……日益增长的淡漠）.md", "type": "file", "size": 1943, "mtime": 1754257750000}, {"name": "hackers_might_target_it.md", "type": "file", "size": 1358, "mtime": 1754168964000}, {"name": "high-stakes_data_practices.md", "type": "file", "size": 748, "mtime": 1754257560000}, {"name": "high-stakes_data_practices（高风险数据实践）.md", "type": "file", "size": 1376, "mtime": 1754257832000}, {"name": "internalized_as.md", "type": "file", "size": 694, "mtime": 1754257560000}, {"name": "internalized_as（被内化为）.md", "type": "file", "size": 1281, "mtime": 1754257873000}, {"name": "in_Compliance_Culture（在合规文化中）.md", "type": "file", "size": 1321, "mtime": 1754257959000}, {"name": "in_Data_Compliance（在数据合规领域）.md", "type": "file", "size": 1164, "mtime": 1754257733000}, {"name": "in_Governance（在治理中）.md", "type": "file", "size": 1449, "mtime": 1754257813000}, {"name": "in_this_system_that_we_need_to_investigate.md", "type": "file", "size": 993, "mtime": 1754171995000}, {"name": "I’ve_noticed.md", "type": "file", "size": 959, "mtime": 1754168882000}, {"name": "Normalizing_the_Extraordinary（非凡事物的常态化）.md", "type": "file", "size": 1433, "mtime": 1754257803000}, {"name": "operational_necessities（运营必需品）.md", "type": "file", "size": 1069, "mtime": 1754257940000}, {"name": "pose_a_major_challenge.md", "type": "file", "size": 741, "mtime": 1754166580000}, {"name": "potential_risks.md", "type": "file", "size": 779, "mtime": 1754188508000}, {"name": "potential_threats.md", "type": "file", "size": 715, "mtime": 1754168702000}, {"name": "proactively_elevate_standards.md", "type": "file", "size": 642, "mtime": 1754257560000}, {"name": "proactively_elevate_standards（主动提升标准）.md", "type": "file", "size": 1443, "mtime": 1754258021000}, {"name": "Proximity_to（长期接触）.md", "type": "file", "size": 1010, "mtime": 1754257894000}, {"name": "public_understanding.md", "type": "file", "size": 775, "mtime": 1754188508000}, {"name": "rapidly_acclimates_to.md", "type": "file", "size": 595, "mtime": 1754257560000}, {"name": "rapidly_acclimates_to（迅速适应）.md", "type": "file", "size": 1384, "mtime": 1754257966000}, {"name": "reassess_the_risks.md", "type": "file", "size": 814, "mtime": 1754168702000}, {"name": "reconsider_this_issue.md", "type": "file", "size": 796, "mtime": 1754168702000}, {"name": "regulatory_demands（监管要求）.md", "type": "file", "size": 1424, "mtime": 1754257974000}, {"name": "rethink_our_approach.md", "type": "file", "size": 1595, "mtime": 1754168996000}, {"name": "rigorous_audits（严格审计）.md", "type": "file", "size": 1694, "mtime": 1754257865000}, {"name": "robust_regulatory_frameworks.md", "type": "file", "size": 694, "mtime": 1754257560000}, {"name": "robust_regulatory_frameworks（强大的监管框架）.md", "type": "file", "size": 1361, "mtime": 1754257903000}, {"name": "short-term_burden.md", "type": "file", "size": 814, "mtime": 1754188507000}, {"name": "significant_milestones（重大里程碑）.md", "type": "file", "size": 1065, "mtime": 1754257846000}, {"name": "Since it’s vulnerable_to_attacks.md", "type": "file", "size": 721, "mtime": 1754168796000}, {"name": "some_security_risks.md", "type": "file", "size": 1018, "mtime": 1754168910000}, {"name": "Some_technical_vulnerabilities.md", "type": "file", "size": 1227, "mtime": 1754172007000}, {"name": "stems_not_only_from..._but_also_from....md", "type": "file", "size": 787, "mtime": 1754257560000}, {"name": "stems_not_only_from..._but_also_from...（不仅源于……还源于….md", "type": "file", "size": 1322, "mtime": 1754257752000}, {"name": "Taking..._for_Granted.md", "type": "file", "size": 644, "mtime": 1754257560000}, {"name": "Taking..._for_Granted（将……视为理所当然）.md", "type": "file", "size": 1656, "mtime": 1754257888000}, {"name": "There_are_some_vulnerabilities.md", "type": "file", "size": 1445, "mtime": 1754172069000}, {"name": "The_Brain's_Adaptive_Blind_Spot（大脑的适应性盲点）.md", "type": "file", "size": 1485, "mtime": 1754257949000}, {"name": "The_Paradox_of_Privilege（特权悖论）.md", "type": "file", "size": 1201, "mtime": 1754257726000}, {"name": "training_data.md", "type": "file", "size": 722, "mtime": 1754188508000}, {"name": "unconscious_normalization.md", "type": "file", "size": 759, "mtime": 1754257560000}, {"name": "unconscious_normalization（无意识的习惯化）.md", "type": "file", "size": 1653, "mtime": 1754257762000}, {"name": "update_it.md", "type": "file", "size": 697, "mtime": 1754168702000}, {"name": "user_consent.md", "type": "file", "size": 800, "mtime": 1754188508000}]}, {"name": "原意表达", "type": "directory", "size": 0, "mtime": 1754304635000, "children": [{"name": "AI发展的长期权衡.md", "type": "file", "size": 880, "mtime": 1754190536000}, {"name": "AI数据收集的伦理问题.md", "type": "file", "size": 732, "mtime": 1754190523000}, {"name": "Data breach is an important issue in today's world.md", "type": "file", "size": 915, "mtime": 1754192098000}, {"name": "Data breaches are a critical issue in today's worl.md", "type": "file", "size": 988, "mtime": 1754192102000}, {"name": "Data security breaches pose a major challenge in t.md", "type": "file", "size": 963, "mtime": 1754192105000}, {"name": "data_compliance_→_data_theft_d.md", "type": "file", "size": 838, "mtime": 1754191045000}, {"name": "enterprise-grade encryption（企业级加密）.md", "type": "file", "size": 895, "mtime": 1754257920000}, {"name": "growing disenchantment with（对……日益增长的淡漠）.md", "type": "file", "size": 1019, "mtime": 1754257928000}, {"name": "high-stakes data practices（高风险数据实践）.md", "type": "file", "size": 846, "mtime": 1754257894000}, {"name": "in Compliance Culture（在合规文化中）.md", "type": "file", "size": 831, "mtime": 1754257898000}, {"name": "in Data Compliance（在数据合规领域）.md", "type": "file", "size": 897, "mtime": 1754257886000}, {"name": "I_am_concerned_with_the_impact.md", "type": "file", "size": 1527, "mtime": 1754190424000}, {"name": "I_see_→_I’ve_noticed_（更地道）.md", "type": "file", "size": 901, "mtime": 1754191648000}, {"name": "I_think_it's_a_burden_in_the_s.md", "type": "file", "size": 1437, "mtime": 1754190345000}, {"name": "I_worry_about_some_big_model_l.md", "type": "file", "size": 2024, "mtime": 1754190387000}, {"name": "someone_can_attack_it_→_it’s_v.md", "type": "file", "size": 1037, "mtime": 1754191052000}, {"name": "Think_of_course_the_government.md", "type": "file", "size": 2725, "mtime": 1754190305000}, {"name": "unconscious normalization（无意识的习惯化）.md", "type": "file", "size": 902, "mtime": 1754257882000}, {"name": "翻译：例如，企业级加密、持续监控系统和全面的数据清单——曾经是前沿技术——如今仅被视为运营必需品。.md", "type": "file", "size": 1698, "mtime": 1754257925000}, {"name": "翻译：每天与高风险数据实践打交道——无论是处理敏感客户信息、管理跨境数据流动，还是降低违规风险——都.md", "type": "file", "size": 1897, "mtime": 1754257924000}, {"name": "翻译：人们对严格数据法规日益增长的淡漠，不仅源于熟悉，还源于对其必要性的无意识习惯化。.md", "type": "file", "size": 1518, "mtime": 1754257891000}, {"name": "公众对AI的认知不足.md", "type": "file", "size": 743, "mtime": 1754190550000}, {"name": "公众教育层面.md", "type": "file", "size": 798, "mtime": 1754191867000}, {"name": "关于AI透明度的需求.md", "type": "file", "size": 688, "mtime": 1754190589000}, {"name": "关于数据权力失衡.md", "type": "file", "size": 711, "mtime": 1754190570000}, {"name": "技术伦理层面.md", "type": "file", "size": 760, "mtime": 1754190618000}, {"name": "技术治理的滞后性（法律跟不上技术发展速度）.md", "type": "file", "size": 1056, "mtime": 1754191882000}, {"name": "结构错误：_with_people_use_→_应说_as_.md", "type": "file", "size": 956, "mtime": 1754190434000}, {"name": "结构混乱：_if_they_obtained..._部分需要.md", "type": "file", "size": 931, "mtime": 1754190398000}, {"name": "介词错误：_concerned_with_the_impac.md", "type": "file", "size": 1074, "mtime": 1754190429000}, {"name": "开头不自然：_Think_of_course_→_应该是_O.md", "type": "file", "size": 790, "mtime": 1754190307000}, {"name": "您想说的：AI公司应该公开数据来源和获取方式。.md", "type": "file", "size": 968, "mtime": 1754190594000}, {"name": "您想说的：小企业_个人在数据安全方面处于弱势，需要制度性保护.md", "type": "file", "size": 1232, "mtime": 1754190575000}, {"name": "时间表达错误：_in_the_short_time_→_应说.md", "type": "file", "size": 1069, "mtime": 1754190375000}, {"name": "时态错误：_lead_to_→_应改为_leading_to.md", "type": "file", "size": 927, "mtime": 1754190321000}, {"name": "使用_since_because_说明原因。.md", "type": "file", "size": 985, "mtime": 1754191066000}, {"name": "术语不完整：_data_complex_framework_.md", "type": "file", "size": 1082, "mtime": 1754190646000}, {"name": "术语不准确：_data_confidence_→_应说_数据.md", "type": "file", "size": 1155, "mtime": 1754190312000}, {"name": "术语错误：_minerals_data_→_可能是_mini.md", "type": "file", "size": 1126, "mtime": 1754190393000}, {"name": "数字公平性（小企业vs大公司的数据资源差距）.md", "type": "file", "size": 991, "mtime": 1754191901000}, {"name": "英文_AI_applications_potential_r.md", "type": "file", "size": 1031, "mtime": 1754190450000}, {"name": "英文_ethical_data_collection_use.md", "type": "file", "size": 1041, "mtime": 1754190415000}, {"name": "英文_government_subsidies_for_sm.md", "type": "file", "size": 1231, "mtime": 1754190338000}, {"name": "英文_I'm_concerned_about_large_A.md", "type": "file", "size": 1940, "mtime": 1754190406000}, {"name": "英文_I'm_concerned_about_the_imp.md", "type": "file", "size": 1578, "mtime": 1754190441000}, {"name": "英文_I_think_it's_a_burden_in_th.md", "type": "file", "size": 1726, "mtime": 1754190362000}, {"name": "英文_Of_course,_the_government_h.md", "type": "file", "size": 2563, "mtime": 1754190327000}, {"name": "英文_short-term_burden_long-term.md", "type": "file", "size": 943, "mtime": 1754190373000}, {"name": "英文：Avoid_redundant_modifiers_l.md", "type": "file", "size": 817, "mtime": 1754190473000}, {"name": "英文：Despite_short-term_costs,_i.md", "type": "file", "size": 1362, "mtime": 1754190545000}, {"name": "英文：Ensure_complete_expression_.md", "type": "file", "size": 775, "mtime": 1754190481000}, {"name": "英文：Governments_should_subsidiz.md", "type": "file", "size": 1780, "mtime": 1754190519000}, {"name": "英文：Maintain_consistent_verb_te.md", "type": "file", "size": 843, "mtime": 1754190467000}, {"name": "英文：Pay_attention_to_concerned_.md", "type": "file", "size": 1115, "mtime": 1754192387000}, {"name": "英文：The_public_heavily_relies_o.md", "type": "file", "size": 1258, "mtime": 1754190566000}, {"name": "英文：Use_technical_terms_accurat.md", "type": "file", "size": 794, "mtime": 1754190489000}, {"name": "英文：You're_concerned_that_AI_co.md", "type": "file", "size": 1459, "mtime": 1754190531000}, {"name": "英文：_AI_firms_must_disclose_dat.md", "type": "file", "size": 1339, "mtime": 1754190602000}, {"name": "英文：_As_AI_becomes_infrastructu.md", "type": "file", "size": 1222, "mtime": 1754190650000}, {"name": "英文：_Data_security_cannot_be_a_.md", "type": "file", "size": 1538, "mtime": 1754190586000}, {"name": "英文：_Instead_of_post-breach_rem.md", "type": "file", "size": 1284, "mtime": 1754190612000}, {"name": "英文：_Training_data'_represents_.md", "type": "file", "size": 1403, "mtime": 1754190626000}, {"name": "用_so_therefore_引出结论。.md", "type": "file", "size": 924, "mtime": 1754191790000}, {"name": "原本想说的，就是说这个系统存在一些的漏洞，我们需要查找一下这.md", "type": "file", "size": 1772, "mtime": 1754192018000}, {"name": "这个系统存在一些的漏洞，我们需要查找一下这些漏洞。然后有有，.md", "type": "file", "size": 1722, "mtime": 1754191057000}, {"name": "政府应资助小企业数据安全.md", "type": "file", "size": 958, "mtime": 1754190503000}, {"name": "知情同意困境（用户协议的实际有效性）.md", "type": "file", "size": 1020, "mtime": 1754191658000}, {"name": "中文 严格的数据法规起初显得至关重要，但久而久之，它们变成了“又一条普通政策”。.md", "type": "file", "size": 1445, "mtime": 1754257922000}, {"name": "中文_AI应用_潜在风险_公众理解.md", "type": "file", "size": 918, "mtime": 1754190455000}, {"name": "中文_当然，政府拥有充足的财政资源，可以补贴企业来支持小公司.md", "type": "file", "size": 2187, "mtime": 1754190335000}, {"name": "中文_道德数据收集_用户同意_训练数据要求.md", "type": "file", "size": 938, "mtime": 1754190420000}, {"name": "中文_短期负担_长期收益_数据治理框架.md", "type": "file", "size": 1020, "mtime": 1754190381000}, {"name": "中文_我担心AI应用的影响，因为随着人们越来越多地使用AI，.md", "type": "file", "size": 1553, "mtime": 1754190446000}, {"name": "中文_我担心像OpenAI这样的大型AI模型，因为它们会收集.md", "type": "file", "size": 1832, "mtime": 1754190411000}, {"name": "中文_我认为这在短期内是个负担，但从长远来看，他们可以建立一.md", "type": "file", "size": 1663, "mtime": 1754190368000}, {"name": "中文_政府对小企业的补贴_数据合规成本_网络安全威胁.md", "type": "file", "size": 1004, "mtime": 1754190358000}, {"name": "中文：'训练数据'的本质是数字劳动剥削——用户生成内容被无偿.md", "type": "file", "size": 1251, "mtime": 1754190622000}, {"name": "中文：_AI公司必须透明化数据采集流程，接受第三方审计，而非.md", "type": "file", "size": 1403, "mtime": 1754190599000}, {"name": "中文：_当AI成为水电般的基础设施，数据素养必须纳入公民基础.md", "type": "file", "size": 1287, "mtime": 1754190643000}, {"name": "中文：_数据安全不应是奢侈品——政府必须通过政策倾斜弥补小企.md", "type": "file", "size": 1391, "mtime": 1754190580000}, {"name": "中文：_与其事后补救数据泄露，不如通过税收减免激励企业提前投.md", "type": "file", "size": 1306, "mtime": 1754190608000}, {"name": "中文：保持句子中的动词时态一致.md", "type": "file", "size": 833, "mtime": 1754190470000}, {"name": "中文：避免像_very_huge_这样的重复修饰.md", "type": "file", "size": 762, "mtime": 1754190477000}, {"name": "中文：您担忧大型AI公司（如OpenAI）在收集用户数据训练.md", "type": "file", "size": 1560, "mtime": 1754190528000}, {"name": "中文：您认为政府有责任通过资金补贴帮助小企业承担数据安全合规.md", "type": "file", "size": 1792, "mtime": 1754190511000}, {"name": "中文：普通用户过度依赖AI却对其潜在风险（如数据滥用）缺乏认.md", "type": "file", "size": 1292, "mtime": 1754190556000}, {"name": "中文：确保完整表达观点.md", "type": "file", "size": 808, "mtime": 1754190486000}, {"name": "中文：虽然建立完善的数据治理体系短期内成本高昂，但长期来看能.md", "type": "file", "size": 1333, "mtime": 1754190540000}, {"name": "中文：注意_concerned_about_和_concer.md", "type": "file", "size": 979, "mtime": 1754190463000}, {"name": "中文：准确使用专业术语.md", "type": "file", "size": 758, "mtime": 1754190496000}, {"name": "重复修饰：_very_huge_→_只需_huge_或_ve.md", "type": "file", "size": 837, "mtime": 1754190315000}]}, {"name": "口语练习", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754308696302, "type": "directory", "children": [{"name": "080209.md", "type": "file", "size": 1455, "mtime": 1754166993000}, {"name": "080301.md", "type": "file", "size": 3889, "mtime": 1754171881000}, {"name": "080302.md", "type": "file", "size": 10172, "mtime": 1754188767000}, {"name": "080401.md", "type": "file", "size": 8867, "mtime": 1754257944000}, {"name": "080402.md", "type": "file", "size": 215, "mtime": 1754258216000}]}, {"name": "具体练习", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754308696302, "type": "directory", "children": [{"name": "CEO应对监管重罚咨询.md", "type": "file", "size": 1180, "mtime": 1754100176000}, {"name": "合同续签合规对接.md", "type": "file", "size": 1118, "mtime": 1754100188000}, {"name": "客户质疑数据合规问题.md", "type": "file", "size": 1105, "mtime": 1754258216000}, {"name": "离职员工数据合规处理.md", "type": "file", "size": 1166, "mtime": 1754094448000}, {"name": "数据合规听证会沟通.md", "type": "file", "size": 1034, "mtime": 1754257521000}, {"name": "数据迁移合规评估.md", "type": "file", "size": 1030, "mtime": 1754145206000}, {"name": "系统安全漏洞评估对话.md", "type": "file", "size": 1136, "mtime": 1754188394000}, {"name": "续约谈判中的数据合规问题.md", "type": "file", "size": 1147, "mtime": 1754105997000}, {"name": "隐私投诉处理.md", "type": "file", "size": 1289, "mtime": 1754137190000}]}, {"name": "录音", "size": 0, "contentHash": "d41d8cd98f00b204e9800998ecf8427e", "mtime": 1754308696304, "type": "directory", "children": [{"name": "录音，2025-08-02 22.03.04.m4a", "type": "file", "size": 105851, "mtime": 1754143387000}, {"name": "录音，2025-08-02 22.03.35.m4a", "type": "file", "size": 109860, "mtime": 1754143419000}, {"name": "录音，2025-08-02 22.06.08.m4a", "type": "file", "size": 103055, "mtime": 1754143571000}, {"name": "录音，2025-08-02 22.07.35.m4a", "type": "file", "size": 225533, "mtime": 1754143668000}, {"name": "录音，2025-08-02 22.16.27.m4a", "type": "file", "size": 141430, "mtime": 1754144193000}, {"name": "录音，2025-08-02 22.24.13.m4a", "type": "file", "size": 155290, "mtime": 1754144661000}, {"name": "录音，2025-08-02 22.44.49.m4a", "type": "file", "size": 158966, "mtime": 1754145897000}, {"name": "录音，2025-08-03 04.46.25.m4a", "type": "file", "size": 231447, "mtime": 1754167598000}]}]}]}, {"name": "闪卡学习", "type": "directory", "size": 0, "mtime": 1754305045000, "children": [{"name": " DPIA（数据保护影响评估） 为什么是“可行性报告”.md", "type": "file", "size": 2888, "mtime": 1753827575000}, {"name": "BCR vs SCCs为什么有时要同时用SCCs和BCR？.md", "type": "file", "size": 4942, "mtime": 1753825505000}, {"name": "GDPR与PIPL.md", "type": "file", "size": 2908, "mtime": 1753827224000}, {"name": "PIPL安全评估和中国版标准合同的适用场景、流程和严格程度完全不同.md", "type": "file", "size": 6057, "mtime": 1753825714000}, {"name": "个人数据处理中的合法性和用户同意.md", "type": "file", "size": 8258, "mtime": 1753825963000}, {"name": "数据处理协议（DPA）和DPIA报告.md", "type": "file", "size": 7227, "mtime": 1753957602000}, {"name": "数据跨境传输合规判断标准（PIPL）.md", "type": "file", "size": 9715, "mtime": 1753825899000}, {"name": "为什么跨境商家会涉及“中国数据出境”？.md", "type": "file", "size": 9272, "mtime": 1753956376000}]}, {"name": "英语笔记", "type": "directory", "size": 0, "mtime": 1754305011000, "children": [{"name": "“精准用词”清单（国际辩论必备）.md", "type": "file", "size": 3493, "mtime": 1752884467000}, {"name": "避免“数据合规废话文学”.md", "type": "file", "size": 17368, "mtime": 1752899702000}, {"name": "常见错误案例及专业表达对照表.md", "type": "file", "size": 4978, "mtime": 1752880123000}, {"name": "对话中的精准回应能力.md", "type": "file", "size": 14995, "mtime": 1752880342000}, {"name": "核心时态规则.md", "type": "file", "size": 4130, "mtime": 1752878922000}, {"name": "侯佩岑用targeted amount of fans（精准受众规模）.md", "type": "file", "size": 4685, "mtime": 1752883425000}, {"name": "介词搭配错误.md", "type": "file", "size": 4999, "mtime": 1752879410000}, {"name": "口音只是入门券，专业才是王炸.md", "type": "file", "size": 6821, "mtime": 1752884504000}, {"name": "夸侯佩岑英文救场时使用的复杂句式结构.md", "type": "file", "size": 25048, "mtime": 1752883105000}, {"name": "数据合规英语的不要重复表达 → 「术语一致性」+「逻辑简洁性」.md", "type": "file", "size": 4233, "mtime": 1753997064000}, {"name": "王冠的辩论策略.md", "type": "file", "size": 6478, "mtime": 1752884448000}, {"name": "西方人更喜欢听小词语和短句子.md", "type": "file", "size": 4789, "mtime": 1753009753000}, {"name": "英语笔记.md", "type": "file", "size": 50, "mtime": 1752878297000}, {"name": "英语演讲案例（尤其是哈佛学生的争议性演讲）.md", "type": "file", "size": 10446, "mtime": 1752885467000}, {"name": "语用规则-术语混淆.md", "type": "file", "size": 5349, "mtime": 1752880091000}, {"name": "组块-英语-反应不过来.md", "type": "file", "size": 11537, "mtime": 1752884882000}]}, {"name": "英语复盘", "type": "directory", "size": 0, "mtime": 1754304611000, "children": [{"name": "你的英语问题主要集中在以下几个方面.md", "type": "file", "size": 24651, "mtime": 1752879895000}, {"name": "英语表达（错题本）.md", "type": "file", "size": 5655, "mtime": 1753330570000}, {"name": "英语复盘.md", "type": "file", "size": 45, "mtime": 1752887814000}]}, {"name": "英语练习插件", "type": "directory", "size": 0, "mtime": 1754305007000, "children": [{"name": "语料提取测试.js", "type": "file", "size": 10136, "mtime": 1754146786000}, {"name": "智能语料提取器.js", "type": "file", "size": 18203, "mtime": 1754147140000}]}, {"name": "20250802_110826.amr", "type": "file", "size": 16102, "mtime": 1754104119000}, {"name": "⛄概念关系.components", "type": "file", "size": 30829, "mtime": 1754100312000}]}