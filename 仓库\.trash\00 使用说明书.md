# 前言
首先，非常感谢您对笔记库的购买和支持！有任何问题可以和我说，我会尽力帮大家解决问题的。因为我还在上学，回复上可能不及时，还请见谅。
本库的插件是直接本人的笔记库移植进来的，都是本人的自用插件，任务管理需要的插件以及本人觉得好用的插件都已启用。其他的插件大家可以自行探索，有些设置是我之前的库里的，需要自己设置一下。

# 使用教程
视频展示： https://www.bilibili.com/video/BV1Rz7Yz2EzL/?share_source=copy_web&vd_source=243c8332bd72b874dc7e6208263485ea
## 任务管理工作流
- 工作流图解：[[任务收集与管理 工作流.excalidraw]]
- 小红书笔记介绍：[obsidian任务管理&日常记录 - 45度的懒橘呀 ]( https://www.xiaohongshu.com/discovery/item/683abbe6000000002101b842?source=webshare&xhsshare=pc_web&xsec_token=ABO5Jr_TOWWhwQgZukSojCDL0fjZ0sYtPGACnvAfL-j70=&xsec_source=pc_share)
- 新的任务根据期限直接写在周视图上或者Inbox和项目笔记里。 周视图中的Task不带#task标签不被Task检索，可以随便写，不用担心造成混乱。而Inbox和项目笔记中的Task带有#task标签，方便使用Task检索和可视化。 
- 根据检索到的Task，每天在周视图写下今日待办。这里的task比较灵活，可以对任务进行细化或者记录。 
- 对于日常记录可以通过脚本进行记录和添加emoji表情，方便对每周做的事情进行快速浏览。
## 各部分文件介绍
主要的功能可以参考小红书笔记中的介绍[obsidian任务管理&日常记录 - 45度的懒橘呀 ]( https://www.xiaohongshu.com/discovery/item/683abbe6000000002101b842?source=webshare&xhsshare=pc_web&xsec_token=ABO5Jr_TOWWhwQgZukSojCDL0fjZ0sYtPGACnvAfL-j70=&xsec_source=pc_share)，这里主要是使用教程的补充说明
## Components插件
-  [[主页.components]]
- Components插件
	- 笔记库的主页搭建依托于Components插件，该插件为付费插件，请到作者Vran处购买插件，**填写序列号即可使用**。强烈推荐！！！
	- 购买链接：[Components插件](https://wxycbt0cjk.feishu.cn/wiki/Hfz9wTuqpiDIkokAMETcoRjnnmh)
	- 版本授权：38元；永久授权98元
- 当然有些宝宝可能不想买，主包这里给大家用白板手搓了一个看板
	- [[主页-手搓版.canvas|主页-手搓版]]
	- 每周需要自己将日记替换，手动更改引用的笔记，有点麻烦。
	- 只能通过quickadd的方法快速记录当天的任务和记录，编辑本周其他的日记需要打开相应的日记中进行编辑，或者在hover editor弹窗中进行修改。
## 快捷添加任务
- 在笔记中开启 task插件面板的快捷键为==ALt+T==。
- Quick add
	- crtl+P打开命令面板
	- 把所有的添加任务的命令已固定![[Pasted image 20250609225525.png]]
		- 今天做什么？：是将task添加到当日日记的# ToDo部分，是不带有#task，显示在周视图上的。
		- 今天做完了什么？：是将文本添加到当日日记的# Done部分，可以在周视图上显示。
		- 任务+1：添加带有#task标签的task到[[任务Inbox]]
		- Insert Task to Project | all：显示所有带有#project的项目笔记笔记，选择想要添加任务的笔记，然后在task面板输入任务。
		-  Insert Task to Project（doing or do）：只显示任务状态为 📥Inbox或者🚀Doing的项目笔记，选择想要添加任务的笔记，然后在task面板输入任务。
## DDL看板颜色和图标修改
![[Pasted image 20250609232136.png]]
![[Pasted image 20250609232426.png]]
## 新建一周的日记
- 在Calendar插件面板双击相应的日期即可新建日记。不要在Components里新建，会报错。![[Pasted image 20250609232555.png]]

## 模版迁移
如果想要把本模板库的功能迁移到自己的笔记库，一般只需要把相应的文件复制粘贴过去就可以了，以下是一些注意事项：
- 注意一下components以及插件设置里的文件夹的设置。
- 模板库中的脚本存放在了components/scripts文件夹内，注意迁移。
- [[🌷主页.components]]中添加Done list的脚本需要启用quickadd才能使用。
- quickadd指令最简单的移植办法就是直接将`任务管理&日常记录-45度的懒橘呀\.obsidian\plugins\quickadd`文件夹拖到自己笔记库的plugins文件夹里，注意修改文件夹位置。或者参考模版库的里的设置自己设置，需要学习一下 quickadd的使用方法。
# 更新记录
- 2025-07-05：[[其他主页/睡眠记录系统.components]]
	- [[睡眠评价系统评价标准]]
# 后续更新
后续会给大家把习惯打卡以及时间记录管理加上，这两个功能我都探索过，但是懒得记录就放弃了。