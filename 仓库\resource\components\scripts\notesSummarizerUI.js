async function notesSummarizer() {
  // 从本地存储读取保存的API密钥
  const savedApiKey = localStorage.getItem('notesSummarizer_apiKey') || '';
  const savedOutputFolder = localStorage.getItem('notesSummarizer_outputFolder') || '';

  // 创建弹窗界面
  const modal = new obsidian.Modal(app);

  modal.titleEl.setText("AI 笔记总结器");

  // 创建表单元素
  const contentEl = modal.contentEl;
  contentEl.empty();
  contentEl.style.padding = "20px";
  contentEl.style.minWidth = "500px";

  // API密钥输入
  const apiKeyDiv = contentEl.createDiv();
  apiKeyDiv.createEl("label", { text: "DeepSeek API密钥：" });
  const apiKeyInput = apiKeyDiv.createEl("input", {
    type: "password",
    placeholder: "请输入DeepSeek API密钥",
    value: savedApiKey
  });
  apiKeyInput.style.width = "100%";
  apiKeyInput.style.marginBottom = "15px";
  
  // 模型选择
  const modelDiv = contentEl.createDiv();
  modelDiv.createEl("label", { text: "模型：" });
  const modelSelect = modelDiv.createEl("select");
  modelSelect.style.width = "100%";
  modelSelect.style.marginBottom = "15px";
  modelSelect.createEl("option", { value: "deepseek-chat", text: "deepseek-chat (推荐)" });
  modelSelect.createEl("option", { value: "deepseek-coder", text: "deepseek-coder" });
  
  // 输出文件夹
  const outputFolderDiv = contentEl.createDiv();
  outputFolderDiv.createEl("label", { text: "输出文件夹：" });
  const outputFolderInput = outputFolderDiv.createEl("input", {
    type: "text",
    placeholder: "留空保存到根目录",
    value: savedOutputFolder
  });
  outputFolderInput.style.width = "100%";
  outputFolderInput.style.marginBottom = "15px";

  // 输出文件名
  const fileNameDiv = contentEl.createDiv();
  fileNameDiv.createEl("label", { text: "输出文件名：" });
  const fileNameInput = fileNameDiv.createEl("input", {
    type: "text",
    placeholder: "笔记总结",
    value: "笔记总结"
  });
  fileNameInput.style.width = "100%";
  fileNameInput.style.marginBottom = "15px";
  
  // 筛选方式说明
  const filterInfoDiv = contentEl.createDiv();
  filterInfoDiv.style.marginBottom = "15px";
  filterInfoDiv.style.padding = "10px";
  filterInfoDiv.style.backgroundColor = "#f0f0f0";
  filterInfoDiv.style.borderRadius = "4px";
  filterInfoDiv.innerHTML = `<strong>筛选方式：</strong>自动筛选所有 <code>创作筛选: true</code> 的笔记<br><small>请在需要总结的笔记的YAML属性中添加 <code>创作筛选: true</code></small>`;
  
  // 自定义提示词
  const promptDiv = contentEl.createDiv();
  promptDiv.createEl("label", { text: "自定义提示词（可选）：" });
  const promptTextarea = promptDiv.createEl("textarea", { 
    placeholder: `留空使用默认提示词，或输入自定义提示词...

默认提示词会：
1. 提取所有笔记的核心观点和关键信息
2. 找出笔记之间的关联性和共同主题
3. 生成结构化的总结报告
4. 包含主要主题、关键发现、重要数据等`
  });
  promptTextarea.style.width = "100%";
  promptTextarea.style.height = "120px";
  promptTextarea.style.marginBottom = "20px";
  promptTextarea.style.resize = "vertical";
  
  // 按钮容器
  const buttonDiv = contentEl.createDiv();
  buttonDiv.style.textAlign = "right";
  
  const cancelButton = buttonDiv.createEl("button", { text: "取消" });
  cancelButton.style.marginRight = "10px";
  
  const confirmButton = buttonDiv.createEl("button", { text: "开始总结" });
  confirmButton.style.backgroundColor = "#007acc";
  confirmButton.style.color = "white";
  confirmButton.style.border = "none";
  confirmButton.style.padding = "8px 16px";
  confirmButton.style.borderRadius = "4px";
  
  // 取消按钮事件
  cancelButton.addEventListener("click", () => {
    modal.close();
  });
  
  // 确认按钮事件
  confirmButton.addEventListener("click", async () => {
    const apiKey = apiKeyInput.value.trim();
    const model = modelSelect.value;
    const outputFolder = outputFolderInput.value.trim();
    const outputFileName = fileNameInput.value.trim() || "笔记总结";
    const customPrompt = promptTextarea.value.trim();

    if (!apiKey) {
      new Notice("请输入DeepSeek API密钥");
      return;
    }

    // 保存API密钥和输出文件夹到本地存储
    localStorage.setItem('notesSummarizer_apiKey', apiKey);
    localStorage.setItem('notesSummarizer_outputFolder', outputFolder);

    modal.close();

    // 执行总结
    await performNotesSummary(apiKey, outputFolder, outputFileName, model, customPrompt);
  });
  
  modal.open();
}

async function performNotesSummary(token, outputFolder, outputFileName, modelType, customPrompt) {
  const model = modelType || "deepseek-chat";
  const outputFile = outputFileName || "笔记总结";
  const folder = outputFolder || "";

  // 获取所有笔记文件
  const allFiles = app.vault.getMarkdownFiles();
  let selectedNotes = [];

  // 筛选包含 创作筛选: true 属性的笔记
  for (const file of allFiles) {
    try {
      const cache = app.metadataCache.getFileCache(file);
      const frontmatter = cache?.frontmatter;

      // 检查是否有 创作筛选: true 属性
      if (frontmatter && frontmatter['创作筛选'] === true) {
        const content = await app.vault.cachedRead(file);
        selectedNotes.push({
          title: file.basename,
          content: content,
          path: file.path
        });
      }
    } catch (error) {
      console.error(`读取文件失败 ${file.path}:`, error);
    }
  }

  if (selectedNotes.length === 0) {
    new Notice(`没有找到包含 "创作筛选: true" 属性的笔记`);
    return;
  }
  
  new Notice(`找到 ${selectedNotes.length} 篇笔记，开始总结...`);
  
  // 构建笔记内容（截取前1000字符避免token过多）
  const notesContent = selectedNotes.map(note => {
    const truncatedContent = note.content.length > 1000 
      ? note.content.substring(0, 1000) + "..." 
      : note.content;
    return `## ${note.title}\n${truncatedContent}`;
  }).join("\n\n---\n\n");
  
  // 默认提示词
  const defaultPrompt = `
请仔细阅读以下笔记内容，并生成一份综合总结报告。

笔记内容：
${notesContent}

请按照以下要求生成总结：
1. 提取所有笔记的核心观点和关键信息
2. 找出笔记之间的关联性和共同主题
3. 识别重要的数据、事实和结论
4. 组织成结构化的总结报告
5. 用清晰的markdown格式输出

总结报告应包含：
- 主要主题和观点
- 关键发现和洞察
- 重要数据和事实
- 结论和建议（如适用）

请用中文输出，保持专业和客观的语调。

特别要求：请在回答中明确标注以下部分，以便提取到笔记属性中：
- "关键发现和洞察"
- "核心数据事实"
- "实施建议"
- "风险警示"
- "结论和建议"


`;

  // 使用自定义提示词或默认提示词
  const analysisPrompt = customPrompt || defaultPrompt;
  
  // 调用DeepSeek API
  const analysisOptions = {
    method: "POST",
    url: "https://api.deepseek.com/chat/completions",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model: model,
      messages: [
        {
          role: "user",
          content: analysisPrompt,
        },
      ],
      temperature: 0.3,
    }),
  };
  
  new Notice("正在生成总结...");
  
  try {
    const response = await obsidian.requestUrl(analysisOptions);
    const result = response.json;
    
    if (!result.choices || result.choices.length === 0) {
      new Notice("AI分析失败 - 没有返回内容");
      return;
    }

    const aiResponse = result.choices[0].message?.content;
    if (!aiResponse) {
      new Notice("AI分析失败 - 响应为空");
      return;
    }

    // 提取各个部分内容
    let keyInsights = "";
    let conclusions = "";
    let coreDataFacts = "";
    let implementationSuggestions = "";
    let riskWarnings = "";

    console.log("AI原始回答:", aiResponse);

    // 清理文本的函数
    function cleanText(text) {
      return text.trim()
        .replace(/>\s*\[![\w\s]*\]\s*/g, '') // 移除 Obsidian callout 标记
        .replace(/>\s*/g, '') // 移除引用符号
        .replace(/\*\*([^*]+)\*\*/g, '$1') // 移除粗体标记
        .replace(/\*([^*]+)\*/g, '$1') // 移除斜体标记
        .replace(/`([^`]+)`/g, '$1') // 移除代码标记
        .replace(/\n+/g, ' ') // 换行转空格
        .replace(/\s+/g, ' ') // 多个空格合并
        .replace(/^[-*•]\s*/gm, '') // 移除列表符号
        .replace(/^\d+\.\s*/gm, '') // 移除数字列表
        .replace(/"/g, '\\"') // 转义双引号
        .substring(0, 300); // 增加长度限制
    }

    // 定义提取模式 - 更宽松的匹配
    const extractionPatterns = {
      keyInsights: [
        /关键发现和洞察[：:\s]*\n*([\s\S]*?)(?=\n\s*[#*-]|\n\s*\d+\.|\n\s*核心数据|实施建议|风险警示|结论和建议|$)/i,
        /关键发现[：:\s]*\n*([\s\S]*?)(?=\n\s*[#*-]|\n\s*\d+\.|\n\s*核心数据|实施建议|风险警示|结论和建议|$)/i,
        /洞察[：:\s]*\n*([\s\S]*?)(?=\n\s*[#*-]|\n\s*\d+\.|\n\s*核心数据|实施建议|风险警示|结论和建议|$)/i,
        /##\s*关键发现和洞察\s*([\s\S]*?)(?=\n#+|$)/i
      ],
      coreDataFacts: [
        /核心数据事实[：:\s]*\n*([\s\S]*?)(?=\n\s*[#*-]|\n\s*\d+\.|\n\s*实施建议|风险警示|结论和建议|$)/i,
        /重要数据[：:\s]*\n*([\s\S]*?)(?=\n\s*[#*-]|\n\s*\d+\.|\n\s*实施建议|风险警示|结论和建议|$)/i,
        /数据事实[：:\s]*\n*([\s\S]*?)(?=\n\s*[#*-]|\n\s*\d+\.|\n\s*实施建议|风险警示|结论和建议|$)/i,
        /##\s*核心数据事实\s*([\s\S]*?)(?=\n#+|$)/i
      ],
      implementationSuggestions: [
        /实施建议[：:\s]*\n*([\s\S]*?)(?=\n\s*[#*-]|\n\s*\d+\.|\n\s*风险警示|结论和建议|$)/i,
        /建议[：:\s]*\n*([\s\S]*?)(?=\n\s*[#*-]|\n\s*\d+\.|\n\s*风险警示|结论和建议|$)/i,
        /##\s*实施建议\s*([\s\S]*?)(?=\n#+|$)/i
      ],
      riskWarnings: [
        /风险警示[：:\s]*\n*([\s\S]*?)(?=\n\s*[#*-]|\n\s*\d+\.|\n\s*结论和建议|$)/i,
        /风险[：:\s]*\n*([\s\S]*?)(?=\n\s*[#*-]|\n\s*\d+\.|\n\s*结论和建议|$)/i,
        /警示[：:\s]*\n*([\s\S]*?)(?=\n\s*[#*-]|\n\s*\d+\.|\n\s*结论和建议|$)/i,
        /##\s*风险警示\s*([\s\S]*?)(?=\n#+|$)/i
      ],
      conclusions: [
        /结论和建议[：:\s]*\n*([\s\S]*?)(?=\n\s*[#*-]|\n\s*\d+\.|$)/i,
        /结论[：:\s]*\n*([\s\S]*?)(?=\n\s*[#*-]|\n\s*\d+\.|$)/i,
        /##\s*结论和建议\s*([\s\S]*?)(?=\n#+|$)/i
      ]
    };

    // 提取各个部分
    for (const [key, patterns] of Object.entries(extractionPatterns)) {
      let extracted = false;
      for (const pattern of patterns) {
        const match = aiResponse.match(pattern);
        if (match && match[1] && match[1].trim()) {
          const rawText = match[1].trim();
          console.log(`${key} 原始匹配内容:`, rawText.substring(0, 100) + "...");

          const cleanedText = cleanText(rawText);
          if (cleanedText && cleanedText.length > 10) { // 确保有实际内容
            switch(key) {
              case 'keyInsights':
                keyInsights = cleanedText;
                console.log("✅ 成功提取关键洞察:", keyInsights);
                break;
              case 'coreDataFacts':
                coreDataFacts = cleanedText;
                console.log("✅ 成功提取核心数据事实:", coreDataFacts);
                break;
              case 'implementationSuggestions':
                implementationSuggestions = cleanedText;
                console.log("✅ 成功提取实施建议:", implementationSuggestions);
                break;
              case 'riskWarnings':
                riskWarnings = cleanedText;
                console.log("✅ 成功提取风险警示:", riskWarnings);
                break;
              case 'conclusions':
                conclusions = cleanedText;
                console.log("✅ 成功提取结论建议:", conclusions);
                break;
            }
            extracted = true;
            break;
          }
        }
      }
      if (!extracted) {
        console.log(`❌ 未能提取 ${key}`);
      }
    }

    // 如果某些字段为空，尝试从整体内容中提取关键句子
    if (!keyInsights || !coreDataFacts) {
      console.log("尝试从整体内容中提取关键信息...");
      const sentences = aiResponse.split(/[。！？\n]/).filter(s => s.trim().length > 20);

      if (!keyInsights && sentences.length > 0) {
        keyInsights = cleanText(sentences[0]);
        console.log("从首句提取关键洞察:", keyInsights);
      }

      if (!coreDataFacts && sentences.length > 1) {
        const dataRelated = sentences.find(s => /数据|统计|比例|百分比|数量/.test(s));
        if (dataRelated) {
          coreDataFacts = cleanText(dataRelated);
          console.log("从数据相关句子提取核心数据事实:", coreDataFacts);
        }
      }
    }

    // 生成总结文件内容
    const sourceNotesList = selectedNotes.map(note => `- [[${note.title}]]`).join('\n');

    const summaryContent = `---
title: "${outputFile}"
created: ${new Date().toISOString().split('T')[0]}
type: notes-summary
source_count: ${selectedNotes.length}
key_insights: "${keyInsights}"
core_data_facts: "${coreDataFacts}"
implementation_suggestions: "${implementationSuggestions}"
risk_warnings: "${riskWarnings}"
conclusions: "${conclusions}"
---

# ${outputFile}

## 源笔记列表
${sourceNotesList}

---

${aiResponse}
`;

    // 创建总结文件
    const summaryFileName = `${outputFile}.md`;
    let finalPath = folder ? `${folder}/${summaryFileName}` : summaryFileName;
    let counter = 1;

    // 如果指定了文件夹，先创建文件夹
    if (folder) {
      try {
        await app.vault.createFolder(folder);
      } catch (e) {
        // 文件夹已存在，忽略错误
      }
    }

    // 检查文件是否存在，如果存在则添加序号
    while (await app.vault.adapter.exists(finalPath)) {
      const nameWithCounter = `${outputFile}_${counter}.md`;
      finalPath = folder ? `${folder}/${nameWithCounter}` : nameWithCounter;
      counter++;
    }

    await app.vault.create(finalPath, summaryContent);
    
    new Notice(`✅ 总结完成！已保存为: ${finalPath}`);
    
    // 可选：自动打开生成的总结文件
    const summaryFile = app.vault.getAbstractFileByPath(finalPath);
    if (summaryFile) {
      app.workspace.getLeaf().openFile(summaryFile);
    }
    
  } catch (error) {
    new Notice("生成总结时发生错误 - 请检查控制台");
    console.error("笔记总结错误:", error);
  }
}

exports.default = {
  entry: notesSummarizer,
  name: "notesSummarizer",
  description: `AI笔记总结器 - 带弹窗界面

使用方法：
直接调用 \`notesSummarizer()\` 即可

功能特性：
- 🎨 友好的弹窗配置界面
- 🏷️ 基于标签自动筛选笔记
- 🤖 DeepSeek AI智能总结和分析
- ✏️ 支持自定义提示词
- 📊 生成结构化报告
- 🔗 保留源笔记链接
- 💾 自动保存并打开结果

支持的模型：
- deepseek-chat (推荐)
- deepseek-coder

使用步骤：
1. 在需要总结的笔记中添加标签 (如 #待总结)
2. 运行 notesSummarizer()
3. 在弹窗中配置参数
4. 点击"开始总结"
5. 查看生成的总结报告

API密钥获取：https://platform.deepseek.com/
`,
};
