---
title: "AES加密工作模式"
source: "[[AES加密]]"
tags: ["AES加密", "模式"]
keywords: ["ECB", "CBC", "CTR", "GCM", "初始化向量", "流密码"]
created: 2025-08-04
type: 原子笔记
---

# AES加密工作模式

- ECB模式（电子密码本）：每个分组独立加密，相同明文产生相同密文，不推荐使用
- CBC模式（密码分组链接）：使用初始化向量（IV），前一个密文块影响后一个明文块，广泛使用的安全模式
- CTR模式（计数器）：将分组密码转换为流密码，支持并行加密，适合高性能场景
- GCM模式（伽罗瓦/计数器）：提供加密和认证，高性能且安全，现代应用的首选模式

---

## 元信息
- **来源笔记**: [[AES加密]]
- **创建时间**: 2025/8/5 05:28:47
- **标签**: #AES加密 #模式
- **关键词**: ECB, CBC, CTR, GCM, 初始化向量, 流密码

## 相关链接
- 返回原笔记: [[AES加密]]
