{"dualConfigs": false, "showConsoleLog": false, "desktop": {"shortDelaySeconds": 5, "longDelaySeconds": 15, "delayBetweenPlugins": 40, "defaultStartupType": null, "showDescriptions": true, "enableDependencies": false, "plugins": {"components": {"startupType": "instant"}, "obsidian-style-settings": {"startupType": "disabled"}, "homepage-plugin": {"startupType": "instant"}, "prompt-library": {"startupType": "instant"}, "infio-copilot": {"startupType": "instant"}, "make-md": {"startupType": "instant"}, "omnisearch": {"startupType": "instant"}, "obsidian-outliner": {"startupType": "instant"}, "obsidian-projects": {"startupType": "instant"}, "remotely-save": {"startupType": "instant"}, "improved-outline": {"startupType": "instant"}, "ai-auto-tags": {"startupType": "instant"}, "colored-tags": {"startupType": "instant"}, "obsidian-excalidraw-plugin": {"startupType": "instant"}, "obsidian-filename-heading-sync": {"startupType": "instant"}, "folder-note-plugin": {"startupType": "instant"}, "form-flow": {"startupType": "instant"}, "header-enhancer": {"startupType": "instant"}, "obsidian-icon-folder": {"startupType": "instant"}, "obsidian-paste-image-rename": {"startupType": "instant"}, "quickadd": {"startupType": "disabled"}, "recent-files-obsidian": {"startupType": "instant"}, "tag-wrangler": {"startupType": "instant"}, "obsidian-tagfolder": {"startupType": "instant"}, "tags-overview": {"startupType": "instant"}, "obsidian-weread-plugin": {"startupType": "instant"}, "editing-toolbar": {"startupType": "instant"}, "templater-obsidian": {"startupType": "disabled"}, "obsidian-tracker": {"startupType": "disabled"}, "sync-vault": {"startupType": "instant"}}}}