{"components": [{"id": "ee4eb301-d7d3-43cf-b9e2-ae46bc51044f", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2024-11-04T02:41:51.560Z", "updateAt": "2024-11-04T02:41:51.560Z", "components": [{"componentId": "b083d8ad-f320-488c-a5f2-f3b9661f9c10", "layout": {"mobile": {"x": 0, "y": 18, "w": 2, "h": 5}, "laptop": {"x": 5, "y": 0, "w": 2, "h": 6}}}, {"componentId": "924b1040-f881-4a2d-9fad-96e18897b485", "layout": {"mobile": {"x": 0, "y": 28, "w": 2, "h": 5}, "laptop": {"x": 9, "y": 0, "w": 3, "h": 6}}}, {"componentId": "dda853f6-7b7f-4e24-908b-836fa1b17048", "layout": {"mobile": {"x": 2, "y": 28, "w": 2, "h": 5}, "laptop": {"x": 7, "y": 0, "w": 2, "h": 6}}}, {"componentId": "c77b03a1-8229-4e8d-a0ff-fa76673ca60f", "layout": {"mobile": {"x": 0, "y": 33, "w": 4, "h": 41}, "laptop": {"x": 0, "y": 15, "w": 12, "h": 24}}}, {"componentId": "97b15ca9-d7e7-4772-b676-ac759af0cb3c", "layout": {"mobile": {"x": 0, "y": 23, "w": 2, "h": 5}, "laptop": {"x": 3, "y": 0, "w": 2, "h": 6}}}, {"componentId": "8b0ab47f-6253-4f6c-b4ec-36e27713accc", "layout": {"mobile": {"x": 2, "y": 18, "w": 2, "h": 10}, "laptop": {"x": 3, "y": 6, "w": 2, "h": 9}}}, {"componentId": "a2d16689-012a-4c77-9a59-c30e8f65c4bc", "layout": {"mobile": {"x": 0, "y": 0, "w": 4, "h": 8}, "laptop": {"x": 0, "y": 0, "w": 3, "h": 7}}}, {"componentId": "4ebbf4f6-5e04-4c53-a4fb-7abc1d8571bc", "layout": {"mobile": {"x": 2, "y": 8, "w": 1, "h": 3}, "laptop": {"x": 2, "y": 7, "w": 1, "h": 2}}}, {"componentId": "de2ec745-2b8b-4e5f-853a-be87470519e8", "layout": {"mobile": {"x": 0, "y": 8, "w": 1, "h": 3}, "laptop": {"x": 1, "y": 7, "w": 1, "h": 2}}}, {"componentId": "4462e5aa-af7e-4091-a8e5-9cff03c28989", "layout": {"mobile": {"x": 2, "y": 11, "w": 1, "h": 3}, "laptop": {"x": 2, "y": 9, "w": 1, "h": 2}}}, {"componentId": "6372c6de-fbdb-47fd-bc24-96dd97a299e5", "layout": {"mobile": {"x": 1, "y": 8, "w": 1, "h": 3}, "laptop": {"x": 0, "y": 7, "w": 1, "h": 2}}}, {"componentId": "10fa8576-2835-4ed1-a688-aab380086dcd", "layout": {"mobile": {"x": 3, "y": 8, "w": 1, "h": 3}, "laptop": {"x": 0, "y": 9, "w": 1, "h": 2}}}, {"componentId": "d0139241-2721-4305-8d3a-789968ffe845", "layout": {"mobile": {"x": 3, "y": 11, "w": 1, "h": 3}, "laptop": {"x": 2, "y": 11, "w": 1, "h": 3}}}, {"componentId": "897b4301-d29b-412c-ba05-0fb0ad09f963", "layout": {"mobile": {"x": 0, "y": 11, "w": 2, "h": 3}, "laptop": {"x": 1, "y": 9, "w": 1, "h": 2}}}, {"componentId": "20c6966d-3f57-4f42-ba07-fbb3966226a5", "layout": {"mobile": {"x": 0, "y": 14, "w": 4, "h": 4}, "laptop": {"x": 0, "y": 11, "w": 2, "h": 3}}}, {"componentId": "392a3cea-2721-4c23-a192-5cf1bb632867", "layout": {"mobile": {"x": 0, "y": 0, "w": 4, "h": 4}, "laptop": {"x": 9, "y": 6, "w": 3, "h": 9}}}, {"componentId": "f3c349d7-9bc3-49e9-8a3f-152587f106c9", "layout": {"mobile": {"x": 0, "y": 0, "w": 4, "h": 4}, "laptop": {"x": 5, "y": 6, "w": 4, "h": 9}}}], "layoutType": "grid", "locked": false}, {"id": "4ebbf4f6-5e04-4c53-a4fb-7abc1d8571bc", "type": "card", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2024-11-04T02:46:02.531Z", "updateAt": "2024-11-04T02:46:02.532Z", "title": "MM", "description": "", "coverFit": "cover", "coverPosition": "left", "clickAction": {"type": "CallCommand", "id": "c4acbe1c-9bf2-4fb0-8359-549f20512fc3", "options": {"commandId": "daily-notes", "commandName": "日记: 打开/创建今天的日记"}}, "icon": "CalendarCheck", "borderColor": "#19425a", "fontColor": "#19425a", "darkBorderColor": "#494949", "darkFontColor": "#979797"}, {"id": "de2ec745-2b8b-4e5f-853a-be87470519e8", "type": "card", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2024-11-04T02:46:02.531Z", "updateAt": "2024-11-04T02:46:02.532Z", "title": "CP", "description": "", "coverFit": "cover", "coverPosition": "left", "clickAction": {"type": "OpenFile", "id": "c4acbe1c-9bf2-4fb0-8359-549f20512fc3", "options": {"filePath": "components.components", "fileName": "components.components", "openPageIn": "tab"}}, "icon": "ChartNoAxesGantt", "borderColor": "#19425a", "fontColor": "#19425a", "darkBorderColor": "#494949", "darkFontColor": "#979797"}, {"id": "4462e5aa-af7e-4091-a8e5-9cff03c28989", "type": "card", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2024-11-04T02:46:02.531Z", "updateAt": "2024-11-04T02:46:02.532Z", "title": "HL", "description": "", "coverFit": "cover", "coverPosition": "left", "clickAction": {"type": "OpenFile", "id": "c4acbe1c-9bf2-4fb0-8359-549f20512fc3", "options": {"filePath": "system/resource/components/health.components", "fileName": "health.components"}}, "icon": "Heart", "borderColor": "#348263", "fontColor": "#348263", "darkBorderColor": "#494949", "darkFontColor": "#979797"}, {"id": "6372c6de-fbdb-47fd-bc24-96dd97a299e5", "type": "card", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2024-11-04T02:46:02.531Z", "updateAt": "2024-11-04T02:46:02.532Z", "title": "OB", "description": "", "coverFit": "cover", "coverPosition": "left", "clickAction": {"type": "OpenFile", "id": "c4acbe1c-9bf2-4fb0-8359-549f20512fc3", "options": {"filePath": "obsidian.components", "fileName": "obsidian.components", "openPageIn": "tab"}}, "icon": "Diamond", "borderColor": "#19425a", "fontColor": "#19425a", "darkBorderColor": "#494949", "darkFontColor": "#979797"}, {"id": "b083d8ad-f320-488c-a5f2-f3b9661f9c10", "type": "count", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": false, "showShadow": true, "createAt": "2024-11-04T02:50:37.868Z", "updateAt": "2024-11-04T02:50:37.868Z", "countValueAlign": "center", "contentPrefix": "", "contentSuffix": "", "countType": "number", "precision": 0, "query": {"valueType": "totalRecords", "value": 100, "filter": {"id": "aa37855d-6735-482b-902d-97d1c549457f", "type": "group", "operator": "and", "conditions": []}}, "totalQuery": {"valueType": "constant", "value": 100, "filter": {"id": "a3b5f97f-8669-4e85-a8ee-152bfcd5dfa9", "type": "group", "operator": "and", "conditions": []}}, "title": "总文件"}, {"id": "924b1040-f881-4a2d-9fad-96e18897b485", "type": "count", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": false, "showShadow": true, "createAt": "2024-11-04T02:50:37.868Z", "updateAt": "2024-11-04T02:50:37.868Z", "countValueAlign": "center", "contentPrefix": "", "contentSuffix": "", "countType": "number", "precision": 0, "query": {"valueType": "totalRecords", "value": 100, "filter": {"id": "aa37855d-6735-482b-902d-97d1c549457f", "type": "group", "operator": "and", "conditions": [{"id": "d6426b1e-107a-44f0-a4fb-266cd50c21ec", "type": "filter", "operator": "contains", "property": "tags", "value": "journal", "conditions": []}]}, "sort": {"orders": [{"id": "51c431fe-ecd5-48d6-a744-17f3f8470276", "property": "${file.basename}", "direction": "desc"}]}}, "totalQuery": {"valueType": "constant", "value": 100, "filter": {"id": "a3b5f97f-8669-4e85-a8ee-152bfcd5dfa9", "type": "group", "operator": "and", "conditions": []}}, "title": "Journal"}, {"id": "05bed34e-9042-4d21-80c7-b16df7661a28", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "日历", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2024-11-04T02:58:05.361Z", "updateAt": "2024-11-04T02:58:05.361Z", "viewType": "calendar", "newPageNameFormat": "{{date:YYYYMMDDHHmmss}} ", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "文件名", "type": "text", "isShow": true, "wrap": false, "options": {}}, {"id": "af69e5df-fbab-4469-a4bb-99d12a6fe6c2", "name": "tasks", "isShow": false, "type": "taskList", "options": {"showTaskList": true, "insertPosition": {"position": "BottomOfNote", "headingLine": ""}}}, {"id": "c75affc4-f02a-4b13-90ca-5bef7e04ed89", "name": "status", "isShow": false, "type": "select", "options": {}}], "templates": [{"id": "d772eecc-85ca-4b3d-acdd-e9b12ef4cfab", "path": "config/page-template/journal-template.md", "name": "journal-template.md", "type": "normal"}], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "itemSize": "components--page-card-medium", "showPropertyName": false, "showAllProperties": false, "items": [], "cover": {"type": "textContent", "source": "default", "fit": "contains", "layout": "components--page-card-cover-landscape"}, "showGrid": false, "heightType": "auto", "heightValue": 600, "dateProperty": "${file.basename}", "calendarViewType": "week", "cardColorFrom": "default", "cardColorProperty": "${file.basename}"}, "filter": {"id": "1f96bc6c-7b3f-451a-84b3-072e87190322", "type": "group", "operator": "and", "conditions": [{"id": "2816dd13-058f-4392-bdd6-5f0e9aeecc6f", "type": "filter", "operator": "contains", "property": "tags", "value": "journal", "conditions": []}]}, "defaultTemplate": "d772eecc-85ca-4b3d-acdd-e9b12ef4cfab"}, {"id": "c77b03a1-8229-4e8d-a0ff-fa76673ca60f", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2024-11-04T03:01:43.648Z", "updateAt": "2024-11-04T03:01:43.648Z", "components": [{"componentId": "154aacc9-c6b3-45f8-9037-7c8a462a6842"}, {"componentId": "d61bb43f-ddfe-464e-8289-4d0795552c85"}, {"componentId": "2b4d1b0d-21af-4037-8f3e-da9d0b9429b1"}, {"componentId": "05bed34e-9042-4d21-80c7-b16df7661a28"}, {"componentId": "13ad95ae-928b-4ea1-8663-0ec7fdd50d69"}, {"componentId": "08f2de56-a932-451f-b175-aff180c4bbb8"}], "layoutType": "tab", "locked": false, "activeComponentId": "d61bb43f-ddfe-464e-8289-4d0795552c85", "layoutOptions": {"headerWidthRatio": -2.6107021625538795}}, {"id": "dda853f6-7b7f-4e24-908b-836fa1b17048", "type": "count", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": false, "showShadow": true, "createAt": "2024-11-04T02:50:37.868Z", "updateAt": "2024-11-04T02:50:37.868Z", "countValueAlign": "center", "contentPrefix": "", "contentSuffix": "", "countType": "number", "precision": 0, "query": {"valueType": "task", "value": 100, "filter": {"id": "aa37855d-6735-482b-902d-97d1c549457f", "type": "group", "operator": "and", "conditions": []}, "sort": {"orders": []}, "aggregationType": "uncompletedTasks"}, "totalQuery": {"valueType": "constant", "value": 100, "filter": {"id": "a3b5f97f-8669-4e85-a8ee-152bfcd5dfa9", "type": "group", "operator": "and", "conditions": []}}, "title": "待办"}, {"id": "97b15ca9-d7e7-4772-b676-ac759af0cb3c", "type": "count", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": false, "showShadow": true, "createAt": "2024-11-04T02:50:37.868Z", "updateAt": "2024-11-04T02:50:37.868Z", "countValueAlign": "center", "contentPrefix": "", "contentSuffix": "MB", "countType": "ratio", "precision": "", "query": {"valueType": "propertyValue", "value": 100, "filter": {"id": "aa37855d-6735-482b-902d-97d1c549457f", "type": "group", "operator": "and", "conditions": []}, "property": "${file.size}"}, "totalQuery": {"valueType": "constant", "value": 1048576, "filter": {"id": "a3b5f97f-8669-4e85-a8ee-152bfcd5dfa9", "type": "group", "operator": "and", "conditions": []}, "sort": {"orders": []}}, "title": "库大小", "fontColor": "#ff6900"}, {"id": "10fa8576-2835-4ed1-a688-aab380086dcd", "type": "card", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2024-11-04T02:46:02.531Z", "updateAt": "2024-11-04T02:46:02.532Z", "title": "LD", "description": "", "coverFit": "cover", "coverPosition": "left", "clickAction": {"type": "RunScript", "id": "c4acbe1c-9bf2-4fb0-8359-549f20512fc3", "options": {"commandId": "theme:use-light", "commandName": "Use light mode", "expression": "switchLightDark()"}}, "icon": "Lightbulb", "fontColor": "#292929", "borderColor": "#292929", "darkBorderColor": "#494949", "darkFontColor": "#979797"}, {"id": "8b0ab47f-6253-4f6c-b4ec-36e27713accc", "type": "timing", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": false, "showShadow": true, "createAt": "2024-11-04T04:11:50.587Z", "updateAt": "2024-11-04T04:11:50.587Z", "showStartDateTime": true, "pictureFit": "cover", "picturePosition": "left", "timeTextPattern": "d", "startDateTime": "2023-12-05 12:12:14", "title": "🎉 使用 Obsidian 已有"}, {"id": "a2d16689-012a-4c77-9a59-c30e8f65c4bc", "type": "time", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2024-11-10T08:10:01.480Z", "updateAt": "2024-11-10T08:10:01.480Z", "showLunar": true, "hideDateInfo": false, "hideSeconds": true, "hideProgress": true, "backgroundImage": {"url": "图片/dfd4a111f338380b66fb041f822cac5f_MD5.png"}, "backgroundColor": "#d4c5c5"}, {"id": "08f2de56-a932-451f-b175-aff180c4bbb8", "type": "multi", "titleAlign": "center", "tabTitle": "专利申请", "maxWidthRatio": -1, "createAt": "2024-04-25T13:50:48.213Z", "updateAt": "2024-04-25T13:50:48.213Z", "backgroundStyle": "none", "widgets": [], "components": [{"componentId": "9c92e4ca-17ab-4a13-b51b-8e31ea5bcbe7"}, {"componentId": "5dad53a9-db07-4ad5-a2f8-cf711f5fae3d"}, {"componentId": "05745b1b-be28-4f12-adba-d08f1baa5cb8"}, {"componentId": "bd4d99b6-bf64-46bd-9213-407180257766"}, {"componentId": "a0d5657a-ffbe-4d5e-8505-7eb545e7cf86"}], "layoutType": "tab", "activeComponentId": "5986cfc9-4bca-4f7b-9e20-bbd1eafb3563"}, {"id": "2b4d1b0d-21af-4037-8f3e-da9d0b9429b1", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "专利法", "maxWidthRatio": -1, "createAt": "2024-04-25T13:50:54.863Z", "updateAt": "2024-04-25T13:50:54.863Z", "backgroundStyle": "none", "viewType": "list", "datasource": {"filter": {"id": "94cd6875-affd-4045-ab01-30a162c2b691", "type": "group", "operator": "and", "conditions": [{"id": "f9df1319-66ff-4c93-816e-41a08cd691f6", "type": "filter", "operator": "contains", "property": "tags", "value": "area", "conditions": []}]}, "dataLimit": 50, "storage": {"location": ""}, "sort": {"orders": []}}, "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "文件名", "type": "text", "isShow": true, "wrap": false, "options": {"statisticType": "count"}}, {"id": "d9f015e7-b6aa-400b-988d-56636ee56e4c", "name": "createTime", "isShow": false, "defaultValue": "", "type": "datetime"}, {"id": "df69d1b5-dd07-442c-b33c-359f88b2ba8a", "name": "tags", "isShow": false, "defaultValue": [], "type": "multiSelect"}, {"id": "6f68e59e-7ad8-4d5a-9778-c126c8b36503", "name": "ingoingLInks", "isShow": false, "type": "formula", "options": {"formula": "countBacklinks()", "width": "266"}, "alias": "", "wrap": true}, {"id": "20af5927-1367-4179-a136-4e47ae8fd34e", "name": "AI总结", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "aiSummary(\"a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN\", \"总结\")"}}}, {"id": "77700f36-8ba0-4d39-bab5-31cae819ad26", "name": "总结", "isShow": true, "type": "text", "options": {}}], "templates": [], "groups": [], "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": false, "itemSize": "components--page-card-small", "showPropertyName": false, "hideFileName": false, "wrapFileName": true, "items": [], "cover": {"type": "none", "value": "cover", "fit": "cover", "layout": "components--page-card-cover-landscape"}, "showGrid": false, "heightType": "auto", "heightValue": 469}, "filter": {"id": "be22bbbf-f3d7-4aeb-ab37-4804945aafd0", "type": "group", "operator": "and", "conditions": [{"id": "b8d41990-31af-4eb0-ae3a-421eb3625399", "type": "filter", "operator": "contains", "property": "${file.path}", "value": "肌肉/裁判文书网/专利法", "conditions": []}, {"id": "93fefdd4-a182-40ee-852a-66606333a2c4", "type": "filter", "operator": "not_contains", "property": "${file.parent}", "value": null, "conditions": []}]}, "loadLimitPerPage": 50, "newPageNameFormat": "", "groupBy": "${file.parent}", "groupStates": {"sort": "manual", "format": "none", "statics": [], "orders": ["__$uncategorizedGroup", "肌肉/裁判文书网/专利法", "肌肉/裁判文书网/专利法/国家专利局流程", "肌肉/裁判文书网/专利法/国家专利局流程/流程笔记", "肌肉/裁判文书网/专利法/微创新", "肌肉/裁判文书网/专利法/专利法律解读", "肌肉/裁判文书网/专利法/专利战争"], "hiddens": ["__$uncategorizedGroup"], "collapseds": []}, "sort": {"orders": [{"id": "97b29c50-88c1-4d81-9bd7-3e60f371eea6", "property": "${file.ctime}", "direction": "desc", "disabled": false}]}}, {"id": "d61bb43f-ddfe-464e-8289-4d0795552c85", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "小猫补光灯", "maxWidthRatio": -1, "createAt": "2024-04-25T13:50:54.863Z", "updateAt": "2024-04-25T13:50:54.863Z", "backgroundStyle": "none", "viewType": "list", "datasource": {"filter": {"id": "94cd6875-affd-4045-ab01-30a162c2b691", "type": "group", "operator": "and", "conditions": [{"id": "f9df1319-66ff-4c93-816e-41a08cd691f6", "type": "filter", "operator": "contains", "property": "tags", "value": "area", "conditions": []}]}, "dataLimit": 50, "storage": {"location": ""}, "sort": {"orders": []}}, "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "文件名", "type": "text", "isShow": true, "wrap": true, "options": {"width": "464"}}, {"id": "d9f015e7-b6aa-400b-988d-56636ee56e4c", "name": "createTime", "isShow": false, "defaultValue": "", "type": "datetime"}, {"id": "df69d1b5-dd07-442c-b33c-359f88b2ba8a", "name": "tags", "isShow": false, "defaultValue": [], "type": "multiSelect"}, {"id": "2e6c71f2-6017-44fb-8819-2dc15c9d22b2", "name": "area", "isShow": false, "type": "link", "options": {}}, {"id": "e0519e4a-b9ec-4da1-a776-8aa91ff793fe", "name": "costTime", "isShow": false, "type": "formula", "options": {"formula": "fixed(duration(prop('createTime') , now(),  'day'), 1)+\"天\""}}, {"id": "c3dbf39a-9489-4d0e-b4b5-f294ab520de0", "name": "${file.words}", "isShow": false, "type": "text", "options": {}}, {"id": "f6168943-eb66-480a-b858-489d5d683f39", "name": "AI总结", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "aiSummary(\"a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN\", \"总结\")"}}}, {"id": "4d1a158c-031a-4547-9bce-41aa9bfcbe07", "name": "总结", "isShow": true, "type": "text", "options": {}}], "templates": [{"id": "b7aebef5-1eb1-49b1-abe5-ca97468e503a", "path": "config/page-template/project-template.md", "name": "project-template.md", "type": "normal"}], "groups": [{"id": "0a1f077c-9115-4009-b679-db19915e0399", "name": "[[Obsidian]]", "items": [], "collapsed": false}, {"id": "4b1ac3ec-b9c9-48f8-9bcb-263fe185615c", "name": "[[components]]", "items": [], "collapsed": false}, {"id": "b9f847f2-88c3-4426-a1a6-faa343c3b867", "name": "[[工作]]", "items": [], "collapsed": false}, {"id": "4c00c8e8-4e94-4ab7-86b9-338f541ea3e2", "name": "[[知识管理]]", "items": [], "collapsed": false}, {"id": "2ef2ecd5-4299-4cde-8864-ff784fd65efb", "name": "[[兴趣探索]]", "items": [], "collapsed": false}, {"id": "ac6d6b57-e5e9-49d9-b90e-f00da7de5490", "name": "__$uncategorizedGroup", "items": [], "collapsed": false}], "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": false, "itemSize": "components--page-card-medium", "showPropertyName": false, "hideFileName": false, "wrapFileName": true, "items": [], "cover": {"type": "none", "value": "cover", "fit": "cover", "layout": "components--page-card-cover-landscape"}}, "filter": {"id": "be22bbbf-f3d7-4aeb-ab37-4804945aafd0", "type": "group", "operator": "or", "conditions": [{"id": "526f6f73-2873-4159-b247-4e05c5f83fc9", "type": "filter", "operator": "contains", "property": "${file.path}", "value": "中小企业隐私工具包/算法（小猫补光灯）", "conditions": []}, {"id": "f05b0092-a069-47be-8075-5f74297707c7", "type": "group", "operator": "and", "conditions": [{"id": "58879ff5-f4b3-401d-97c2-3a9d24bec6f4", "type": "filter", "operator": "contains", "property": "${file.basename}", "value": "算法", "conditions": []}, {"id": "56f81336-2406-41ad-80e4-18e82affb977", "type": "filter", "operator": "not_contains", "property": "${file.parent}", "value": "肌肉/英语/英语/资料/算法", "conditions": []}, {"id": "54b023fa-2cbd-48a2-81d7-9235f4f13377", "type": "filter", "operator": "not_contains", "property": "${file.parent}", "value": "肌肉/演讲", "conditions": []}, {"id": "e3b73a09-c05d-4b96-ba22-2ef223efe75b", "type": "filter", "operator": "not_contains", "property": "${file.path}", "value": "读书会/讲书/写作习惯", "conditions": []}, {"id": "b042b936-37a2-4eba-a543-7cb196ccd4e6", "type": "filter", "operator": "not_contains", "property": "${file.path}", "value": "读书会/讲书/书稿/版权", "conditions": []}]}]}, "loadLimitPerPage": 25, "newPageNameFormat": "{{date: YYMMDD}} ", "groupBy": "area", "defaultTemplate": "b7aebef5-1eb1-49b1-abe5-ca97468e503a", "colorfulGroups": false, "sort": {"orders": [{"id": "06e69a68-fa49-4e72-944c-1863f824ad45", "property": "${file.ctime}", "direction": "desc", "disabled": false}]}}, {"id": "d0139241-2721-4305-8d3a-789968ffe845", "type": "card", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2024-11-04T02:46:02.531Z", "updateAt": "2024-11-04T02:46:02.532Z", "title": "OFF", "description": "", "coverFit": "cover", "coverPosition": "left", "clickAction": {"type": "CallCommand", "id": "c4acbe1c-9bf2-4fb0-8359-549f20512fc3", "options": {"filePath": "system/resource/components/health.components", "fileName": "health.components", "commandId": "app:reload", "commandName": "Reload app without saving"}}, "icon": "Power", "borderColor": "#101010", "darkBorderColor": "#494949", "darkFontColor": "#979797", "fontColor": "#1c1c1c"}, {"id": "897b4301-d29b-412c-ba05-0fb0ad09f963", "type": "card", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2024-11-04T02:46:02.531Z", "updateAt": "2024-11-04T02:46:02.532Z", "title": "OD", "description": "", "coverFit": "cover", "coverPosition": "left", "clickAction": {"type": "CallTemplater", "id": "c4acbe1c-9bf2-4fb0-8359-549f20512fc3", "options": {"filePath": "system/resource/templater/Components License Generate.md", "fileName": "Components License Generate.md"}}, "icon": "BatteryCharging", "borderColor": "#056f48", "fontColor": "#056f48", "darkBorderColor": "#494949", "darkFontColor": "#979797"}, {"id": "20c6966d-3f57-4f42-ba07-fbb3966226a5", "type": "count", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2024-11-04T02:50:37.868Z", "updateAt": "2024-11-04T02:50:37.868Z", "countValueAlign": "center", "contentPrefix": "", "contentSuffix": "w 词", "countType": "ratio", "precision": 0, "query": {"valueType": "propertyValue", "value": 100, "filter": {"id": "aa37855d-6735-482b-902d-97d1c549457f", "type": "group", "operator": "and", "conditions": [{"id": "edea7db6-3438-48ae-8483-fa7e684b5403", "type": "filter", "operator": "not_contains", "property": "tags", "value": "excalidraw", "conditions": []}, {"id": "ae2858ff-8041-4624-b94c-3ea018410c80", "type": "filter", "operator": "not_equals", "property": "type", "value": "trade", "conditions": []}]}, "sort": {"orders": []}, "property": "${file.words}"}, "totalQuery": {"valueType": "constant", "value": 10000, "filter": {"id": "a3b5f97f-8669-4e85-a8ee-152bfcd5dfa9", "type": "group", "operator": "and", "conditions": []}, "sort": {"orders": []}}, "title": "", "borderColor": "#7a3302", "darkBorderColor": "#494949", "fontColor": "#7a3302", "darkFontColor": "#979797"}, {"id": "154aacc9-c6b3-45f8-9037-7c8a462a6842", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "icost", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2024-12-01T12:08:40.737Z", "updateAt": "2024-12-01T12:08:40.737Z", "viewType": "list", "newPageNameFormat": "{{date: YYMMDD}} ", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "文件名", "type": "text", "isShow": true, "wrap": true, "options": {"width": "531"}}, {"id": "638bb029-6a73-4827-8d6f-5367903570de", "name": "${file.ctime}", "isShow": false, "type": "datetime", "options": {}}, {"id": "7dca9ae9-aebc-4cea-beee-bd1fef6e5a94", "name": "createTime", "isShow": false, "type": "datetime", "options": {}}, {"id": "6b2c85cb-821d-4b26-84d6-77ec238b0d27", "name": "tags", "isShow": false, "type": "multiSelect", "options": {}}, {"id": "4d5a8fa8-e809-4409-8634-49e70c4f1af6", "name": "AI总结", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "aiSummary(\"a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN\", \"总结\")"}}}, {"id": "bcbd81e5-b9e9-4644-a5e9-53ee143aaa24", "name": "总结", "isShow": true, "type": "text", "options": {}}], "templates": [], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": false, "itemSize": "components--page-card-medium", "showPropertyName": false, "showAllProperties": false, "items": [], "cover": {"type": "textContent", "source": "default", "fit": "contains", "layout": "components--page-card-cover-landscape", "position": "top"}, "showGrid": false, "heightType": "auto", "heightValue": 600}, "filter": {"id": "515327b4-ef5e-4c34-af49-926cd6b4a08f", "type": "group", "operator": "and", "conditions": [{"id": "1ba7b565-5507-43b5-ac3a-14f6f9855fb9", "type": "filter", "operator": "contains", "property": "${file.path}", "value": "工作流程优化/AI记账软件/工具篇", "conditions": []}]}, "sort": {"orders": [{"id": "6197eace-fa54-43ac-87fe-53cb31a0b18b", "property": "${file.ctime}", "direction": "desc"}]}, "newPageLocation": {"location": "inbox"}}, {"id": "5dad53a9-db07-4ad5-a2f8-cf711f5fae3d", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "AI申请", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-06-25T03:07:18.321Z", "updateAt": "2025-06-25T03:07:18.321Z", "viewType": "list", "newPageNameFormat": "{{date:YYYYMMDDHHmmss}} ", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "文件名", "type": "text", "isShow": true, "wrap": false, "options": {"width": "444"}}, {"id": "c5b4ec14-d918-4ca2-a9d4-bb8d3a6fd794", "name": "AI总结", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "aiSummary(\"a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN\", \"总结\")"}}}, {"id": "6a2ffa41-0ceb-46a8-bcae-1bd39515ff0d", "name": "总结", "isShow": true, "type": "text", "options": {}}], "templates": [], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "itemSize": "components--page-card-small", "showPropertyName": false, "showAllProperties": false, "items": [], "cover": {"type": "textContent", "source": "default", "fit": "contains", "layout": "components--page-card-cover-landscape", "position": "top"}, "pinFiltersToMenuBar": false, "showGrid": false, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "filter": {"id": "1271af1a-b277-4c03-8e5c-5d5b19d7dd46", "type": "group", "operator": "and", "conditions": [{"id": "c9b19371-4775-4de8-ad6a-9c092747b0f8", "type": "filter", "operator": "contains", "property": "${file.path}", "value": "肌肉/裁判文书网/可信时间戳/专利申请", "conditions": []}]}}, {"id": "392a3cea-2721-4c23-a192-5cf1bb632867", "type": "card", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2025-06-25T08:51:38.394Z", "updateAt": "2025-06-25T08:51:38.394Z", "title": "", "description": "", "coverFit": "contains", "coverPosition": "top", "clickAction": {"type": "CallCommand", "id": "cb23bb1f-a5ce-4dbc-b397-f158d0b5e6d7", "options": {}}, "cover": "图片/1632fd9e625c59eaffb50979a035b9fd_MD5.jpeg"}, {"id": "f3c349d7-9bc3-49e9-8a3f-152587f106c9", "type": "card", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2025-06-25T08:51:38.394Z", "updateAt": "2025-06-25T08:51:38.394Z", "title": "", "description": "", "coverFit": "contains", "coverPosition": "top", "clickAction": {"type": "CallCommand", "id": "cb23bb1f-a5ce-4dbc-b397-f158d0b5e6d7", "options": {}}, "cover": "图片/小猫补光灯算法—合规方法论写成文档，申请实用新型专利-1.png"}, {"id": "13ad95ae-928b-4ea1-8663-0ec7fdd50d69", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "微创新", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-06-25T17:53:06.082Z", "updateAt": "2025-06-25T17:53:06.082Z", "viewType": "list", "newPageNameFormat": "{{date:YYYYMMDDHHmmss}} ", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "文件名", "type": "text", "isShow": true, "wrap": false, "options": {}}, {"id": "01f273dd-a3b1-4370-a5b8-a47486f12bef", "name": "AI总结", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "aiSummary(\"a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN\", \"总结\")"}}}, {"id": "df4ddc05-e033-49e8-8ba4-e41c763e34d7", "name": "总结", "isShow": true, "type": "text", "options": {}}], "templates": [], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "itemSize": "components--page-card-small", "showPropertyName": false, "showAllProperties": false, "items": [], "cover": {"type": "none", "source": "default", "fit": "contains", "layout": "components--page-card-cover-landscape", "position": "top"}, "pinFiltersToMenuBar": false, "showGrid": false, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "filter": {"id": "26dcdc4d-f9f5-458e-93ac-eca441bf0579", "type": "group", "operator": "and", "conditions": [{"id": "15345706-af3c-4813-a6b3-d21c0563df87", "type": "filter", "operator": "contains", "property": "${file.parent}", "value": "肌肉/裁判文书网/专利法/微创新", "conditions": []}]}, "sort": {"orders": [{"id": "cf12b9ab-2f38-4b63-8887-2aadd3358327", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}}, {"id": "05745b1b-be28-4f12-adba-d08f1baa5cb8", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "国家专利局流程", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-06-25T03:07:18.321Z", "updateAt": "2025-06-25T03:07:18.321Z", "viewType": "list", "newPageNameFormat": "{{date:YYYYMMDDHHmmss}} ", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "文件名", "type": "text", "isShow": true, "wrap": false, "options": {"width": "444"}}, {"id": "c5b4ec14-d918-4ca2-a9d4-bb8d3a6fd794", "name": "AI总结", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "aiSummary(\"a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN\", \"总结\")"}}}, {"id": "6a2ffa41-0ceb-46a8-bcae-1bd39515ff0d", "name": "总结", "isShow": true, "type": "text", "options": {}}], "templates": [], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "itemSize": "components--page-card-small", "showPropertyName": false, "showAllProperties": false, "items": [], "cover": {"type": "textContent", "source": "default", "fit": "contains", "layout": "components--page-card-cover-landscape", "position": "top"}, "pinFiltersToMenuBar": false, "showGrid": false, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "filter": {"id": "1271af1a-b277-4c03-8e5c-5d5b19d7dd46", "type": "group", "operator": "and", "conditions": [{"id": "c9b19371-4775-4de8-ad6a-9c092747b0f8", "type": "filter", "operator": "contains", "property": "${file.path}", "value": "肌肉/裁判文书网/专利法/国家专利局流程", "conditions": []}, {"id": "ae2eeaa3-eaba-4ba5-b35e-9a33df4572d5", "type": "filter", "operator": "contains", "property": "${file.tags}", "value": "", "conditions": []}]}, "sort": {"orders": [{"id": "5e02a0c1-8ee6-4897-84c8-b154e06157e3", "property": "${file.ctime}", "direction": "asc", "disabled": false}]}}, {"id": "bd4d99b6-bf64-46bd-9213-407180257766", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "流程笔记", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-06-25T03:07:18.321Z", "updateAt": "2025-06-25T03:07:18.321Z", "viewType": "list", "newPageNameFormat": "{{date:YYYYMMDDHHmmss}} ", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "文件名", "type": "text", "isShow": true, "wrap": false, "options": {"width": "444"}}, {"id": "c5b4ec14-d918-4ca2-a9d4-bb8d3a6fd794", "name": "AI总结", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "aiSummary(\"a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN\", \"总结\")"}}}, {"id": "6a2ffa41-0ceb-46a8-bcae-1bd39515ff0d", "name": "总结", "isShow": true, "type": "text", "options": {}}], "templates": [], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "itemSize": "components--page-card-small", "showPropertyName": false, "showAllProperties": false, "items": [], "cover": {"type": "textContent", "source": "default", "fit": "contains", "layout": "components--page-card-cover-landscape", "position": "top"}, "pinFiltersToMenuBar": false, "showGrid": false, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "filter": {"id": "1271af1a-b277-4c03-8e5c-5d5b19d7dd46", "type": "group", "operator": "and", "conditions": [{"id": "c9b19371-4775-4de8-ad6a-9c092747b0f8", "type": "filter", "operator": "contains", "property": "${file.path}", "value": "肌肉/裁判文书网/专利法/国家专利局流程/流程笔记", "conditions": []}]}}, {"id": "9c92e4ca-17ab-4a13-b51b-8e31ea5bcbe7", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "进度表", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-06-25T03:07:18.321Z", "updateAt": "2025-06-25T03:07:18.321Z", "viewType": "list", "newPageNameFormat": "{{date:YYYYMMDDHHmmss}} ", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "文件名", "type": "text", "isShow": true, "wrap": false, "options": {"width": "444"}}, {"id": "c5b4ec14-d918-4ca2-a9d4-bb8d3a6fd794", "name": "AI总结", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "aiSummary(\"a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN\", \"总结\")"}}}, {"id": "6a2ffa41-0ceb-46a8-bcae-1bd39515ff0d", "name": "总结", "isShow": true, "type": "text", "options": {}}], "templates": [], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "itemSize": "components--page-card-small", "showPropertyName": false, "showAllProperties": false, "items": [], "cover": {"type": "textContent", "source": "default", "fit": "contains", "layout": "components--page-card-cover-landscape", "position": "top"}, "pinFiltersToMenuBar": false, "showGrid": false, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "filter": {"id": "1271af1a-b277-4c03-8e5c-5d5b19d7dd46", "type": "group", "operator": "or", "conditions": [{"id": "c9b19371-4775-4de8-ad6a-9c092747b0f8", "type": "filter", "operator": "contains", "property": "${file.path}", "value": "肌肉/裁判文书网/可信时间戳/专利申请/专利库", "conditions": []}, {"id": "e7b14dee-272a-481a-9ecb-7efc054abdd4", "type": "filter", "operator": "contains", "property": "${file.basename}", "value": "专利申请进度表", "conditions": []}]}}, {"id": "a0d5657a-ffbe-4d5e-8505-7eb545e7cf86", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "可信时间戳", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-06-25T03:07:18.321Z", "updateAt": "2025-06-25T03:07:18.321Z", "viewType": "list", "newPageNameFormat": "{{date:YYYYMMDDHHmmss}} ", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "文件名", "type": "text", "isShow": true, "wrap": false, "options": {"width": "444"}}, {"id": "c5b4ec14-d918-4ca2-a9d4-bb8d3a6fd794", "name": "AI总结", "isShow": true, "type": "button", "options": {"action": {"type": "runScript", "properties": [], "expression": "aiSummary(\"a6ff22a8cdac4123a9d9a8ea3779009c.aOZZo3lqUQxXgZkN\", \"总结\")"}}}, {"id": "6a2ffa41-0ceb-46a8-bcae-1bd39515ff0d", "name": "总结", "isShow": true, "type": "text", "options": {}}], "templates": [], "groups": [], "colorfulGroups": false, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "itemSize": "components--page-card-small", "showPropertyName": false, "showAllProperties": false, "items": [], "cover": {"type": "textContent", "source": "default", "fit": "contains", "layout": "components--page-card-cover-landscape", "position": "top"}, "pinFiltersToMenuBar": false, "showGrid": false, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "statics": [], "orders": [], "hiddens": [], "collapseds": []}, "filter": {"id": "1271af1a-b277-4c03-8e5c-5d5b19d7dd46", "type": "group", "operator": "and", "conditions": [{"id": "46a3b91b-b8a1-479a-949b-9d4118304444", "type": "filter", "operator": "contains", "property": "${file.parent}", "value": "肌肉/裁判文书网/可信时间戳", "conditions": []}, {"id": "ce125028-d019-4403-903d-7e9d56414d60", "type": "filter", "operator": "not_contains", "property": "${file.parent}", "value": "肌肉/裁判文书网/可信时间戳/专利申请", "conditions": []}]}, "sort": {"orders": [{"id": "b0ef9f1b-01e0-4968-8cf7-e556b36faec8", "property": "${file.mtime}", "direction": "desc", "disabled": false}]}}], "rootComponentId": "ee4eb301-d7d3-43cf-b9e2-ae46bc51044f"}