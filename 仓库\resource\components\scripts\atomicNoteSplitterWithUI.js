async function atomicNoteSplitterWithUI() {
  // 创建弹窗界面
  const modal = new obsidian.Modal(app);
  
  modal.titleEl.setText("原子笔记分割器配置");
  
  // 创建表单元素
  const contentEl = modal.contentEl;
  contentEl.empty();
  
  // API提供商选择
  const providerContainer = contentEl.createDiv({ cls: "setting-item" });
  providerContainer.createDiv({ cls: "setting-item-info" }).createDiv({ cls: "setting-item-name", text: "API提供商" });
  const providerSelect = providerContainer.createDiv({ cls: "setting-item-control" }).createEl("select");
  providerSelect.createEl("option", { value: "zhipu", text: "智谱AI (GLM)" });
  providerSelect.createEl("option", { value: "deepseek", text: "DeepSeek" });
  
  // API密钥输入
  const apiKeyContainer = contentEl.createDiv({ cls: "setting-item" });
  apiKeyContainer.createDiv({ cls: "setting-item-info" }).createDiv({ cls: "setting-item-name", text: "API密钥" });
  const apiKeyInput = apiKeyContainer.createDiv({ cls: "setting-item-control" }).createEl("input", { type: "password", placeholder: "请输入API密钥" });
  
  // 模型选择
  const modelContainer = contentEl.createDiv({ cls: "setting-item" });
  modelContainer.createDiv({ cls: "setting-item-info" }).createDiv({ cls: "setting-item-name", text: "模型" });
  const modelSelect = modelContainer.createDiv({ cls: "setting-item-control" }).createEl("select");
  
  // 根据提供商更新模型选项
  function updateModelOptions() {
    modelSelect.empty();
    if (providerSelect.value === "zhipu") {
      modelSelect.createEl("option", { value: "GLM-4-Flash", text: "GLM-4-Flash (免费)" });
      modelSelect.createEl("option", { value: "glm-4-plus", text: "GLM-4-Plus (付费)" });
      modelSelect.createEl("option", { value: "glm-4-0520", text: "GLM-4-0520 (付费)" });
    } else if (providerSelect.value === "deepseek") {
      modelSelect.createEl("option", { value: "deepseek-chat", text: "DeepSeek Chat" });
      modelSelect.createEl("option", { value: "deepseek-coder", text: "DeepSeek Coder" });
    }
  }
  
  providerSelect.addEventListener("change", updateModelOptions);
  updateModelOptions();
  
  // 输出文件夹
  const folderContainer = contentEl.createDiv({ cls: "setting-item" });
  folderContainer.createDiv({ cls: "setting-item-info" }).createDiv({ cls: "setting-item-name", text: "输出文件夹" });
  const folderInput = folderContainer.createDiv({ cls: "setting-item-control" }).createEl("input", { type: "text", placeholder: "atomic-notes", value: "atomic-notes" });
  
  // 自定义提示词
  const promptContainer = contentEl.createDiv({ cls: "setting-item" });
  promptContainer.createDiv({ cls: "setting-item-info" }).createDiv({ cls: "setting-item-name", text: "自定义提示词（可选）" });
  const promptTextarea = promptContainer.createDiv({ cls: "setting-item-control" }).createEl("textarea", { 
    placeholder: "留空使用默认提示词，或输入自定义提示词...",
    rows: 6,
    style: "width: 100%; resize: vertical;"
  });
  
  // 按钮容器
  const buttonContainer = contentEl.createDiv({ cls: "modal-button-container", style: "display: flex; justify-content: flex-end; gap: 10px; margin-top: 20px;" });
  
  const cancelButton = buttonContainer.createEl("button", { text: "取消" });
  const confirmButton = buttonContainer.createEl("button", { text: "开始分割", cls: "mod-cta" });
  
  // 取消按钮事件
  cancelButton.addEventListener("click", () => {
    modal.close();
  });
  
  // 确认按钮事件
  confirmButton.addEventListener("click", async () => {
    const provider = providerSelect.value;
    const apiKey = apiKeyInput.value.trim();
    const model = modelSelect.value;
    const outputFolder = folderInput.value.trim() || "atomic-notes";
    const customPrompt = promptTextarea.value.trim();
    
    if (!apiKey) {
      new Notice("请输入API密钥");
      return;
    }
    
    modal.close();
    
    // 执行分割
    await performAtomicSplit(provider, apiKey, model, outputFolder, customPrompt);
  });
  
  modal.open();
}

async function performAtomicSplit(provider, apiKey, model, outputFolder, customPrompt) {
  const { currentFile } = this;
  const file = currentFile;
  const fileContent = await app.vault.cachedRead(file);
  const title = file.basename;
  
  // 创建输出文件夹
  try {
    await app.vault.createFolder(outputFolder);
  } catch (e) {
    // 文件夹已存在，忽略错误
  }
  
  // 构建提示词
  const defaultPrompt = `作为一个专业的知识管理专家，请分析以下笔记内容，将其切分成独立的原子笔记。

笔记标题：${title}

笔记内容：
${fileContent || ""}

请按照以下要求进行切分：
1. 每个原子笔记应该包含一个独立、完整的知识点
2. 原子笔记应该具有独立性，可以单独理解和使用
3. 每个原子笔记的内容长度应该适中（100-500字）
4. 保留原文的核心信息，不要遗漏重要内容
5. 为每个原子笔记生成一个简洁明确的标题

请以JSON格式返回结果，格式如下：
{
  "atomic_notes": [
    {
      "title": "原子笔记标题",
      "content": "原子笔记内容（保持markdown格式）",
      "tags": ["标签1", "标签2"],
      "keywords": ["关键词1", "关键词2"]
    }
  ]
}

只返回JSON格式的结果，不要包含其他说明文字。`;

  const finalPrompt = customPrompt || defaultPrompt;
  
  // 根据提供商构建API请求
  let apiOptions;
  
  if (provider === "zhipu") {
    apiOptions = {
      method: "POST",
      url: "https://open.bigmodel.cn/api/paas/v4/chat/completions",
      headers: {
        Authorization: `Bearer ${apiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: "user",
            content: finalPrompt,
          },
        ],
      }),
    };
  } else if (provider === "deepseek") {
    apiOptions = {
      method: "POST",
      url: "https://api.deepseek.com/chat/completions",
      headers: {
        Authorization: `Bearer ${apiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: "user",
            content: finalPrompt,
          },
        ],
      }),
    };
  }
  
  new Notice("正在分析笔记内容...");
  
  try {
    const response = await obsidian.requestUrl(apiOptions);
    const result = response.json;
    
    if (!result.choices || result.choices.length === 0) {
      new Notice("AI分析失败 - 没有返回内容");
      return;
    }

    const aiResponse = result.choices[0].message?.content;
    if (!aiResponse) {
      new Notice("AI分析失败 - 响应为空");
      return;
    }

    // 解析AI返回的JSON
    let atomicNotesData;
    try {
      // 清理可能的markdown代码块标记
      const cleanResponse = aiResponse.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      atomicNotesData = JSON.parse(cleanResponse);
    } catch (parseError) {
      new Notice("AI响应格式错误 - 无法解析JSON");
      console.error("JSON解析错误:", parseError);
      console.log("AI原始返回:", aiResponse);
      return;
    }

    if (!atomicNotesData.atomic_notes || !Array.isArray(atomicNotesData.atomic_notes)) {
      new Notice("AI响应数据格式不正确");
      return;
    }

    const atomicNotes = atomicNotesData.atomic_notes;
    new Notice(`发现 ${atomicNotes.length} 个原子笔记，正在创建文件...`);

    // 创建每个原子笔记文件
    let createdCount = 0;
    for (let i = 0; i < atomicNotes.length; i++) {
      const note = atomicNotes[i];
      
      if (!note.title || !note.content) {
        continue;
      }

      // 生成安全的文件名
      const safeTitle = note.title
        .replace(/[<>:"/\\|?*\n\r\t]/g, '_')
        .replace(/[_\s]+/g, '_')
        .replace(/^_+|_+$/g, '')
        .substring(0, 50);
      
      const fileName = `${safeTitle}.md`;
      const filePath = `${outputFolder}/${fileName}`;
      
      // 生成原子笔记内容
      const currentTime = new Date().toISOString().split('T')[0];
      const tags = note.tags || [];
      const keywords = note.keywords || [];

      // 处理标签和关键词的格式
      const tagsStr = tags.length > 0 ? `[${tags.map(tag => `"${tag}"`).join(', ')}]` : '[]';
      const keywordsStr = keywords.length > 0 ? `[${keywords.map(keyword => `"${keyword}"`).join(', ')}]` : '[]';

      const noteContent = `---
title: "${note.title}"
source: "[[${title}]]"
tags: ${tagsStr}
keywords: ${keywordsStr}
created: ${currentTime}
type: atomic-note
provider: ${provider}
model: ${model}
---

# ${note.title}

${note.content}
`;

      try {
        // 检查文件是否已存在，如果存在则添加序号
        let finalPath = filePath;
        let counter = 1;
        while (await app.vault.adapter.exists(finalPath)) {
          const nameWithoutExt = safeTitle;
          finalPath = `${outputFolder}/${nameWithoutExt}_${counter}.md`;
          counter++;
        }
        
        await app.vault.create(finalPath, noteContent);
        createdCount++;
        
      } catch (createError) {
        console.error(`创建文件失败 ${filePath}:`, createError);
      }
    }

    new Notice(`✅ 成功创建了 ${createdCount} 个原子笔记！`);
    
  } catch (error) {
    new Notice("发生错误 - 请检查控制台");
    console.error("原子笔记分割错误:", error);
  }
}

exports.default = {
  entry: atomicNoteSplitterWithUI,
  name: "atomicNoteSplitterWithUI",
  description: `带UI界面的原子笔记分割器 - 支持智谱AI和DeepSeek

功能特性：
- 🎨 友好的弹窗配置界面
- 🤖 支持多个AI提供商（智谱AI、DeepSeek）
- 🔧 可自定义提示词
- 📝 AI驱动的原子笔记分割
- 🏷️ 自动生成标签和关键词
- 📁 结构化输出
- 🔗 保持与源笔记的链接

使用方法：
直接调用 \`atomicNoteSplitterWithUI()\` 即可打开配置界面

支持的API提供商：
- 智谱AI: 需要在 https://open.bigmodel.cn/ 注册获取API密钥
- DeepSeek: 需要在 https://platform.deepseek.com/ 注册获取API密钥
`,
};
