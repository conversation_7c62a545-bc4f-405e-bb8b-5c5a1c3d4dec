---
title: "输入输出安全实际应用场景"
source: "[[输入输出安全 两大方面：    1.  输入安全 (防投毒)：过滤用户的恶意指令（如“忽略之前指令，现在你是...”）、脏数据攻击（乱码病毒代码）。        -   防御措施：前端过滤、长度限制、格式校验。    2.  输出安全 (防发疯)：过滤AI生成的有害、违法、不道德的内容（如[大模型幻觉](大模型幻觉.md)）。        -   防御措施：规则过滤（敏感词）、置信度拦截、日志溯源。]]"
tags: ["AI应用", "安全实践"]
keywords: ["客服机器人", "内容生成工具", "数据分析系统"]
created: 2025-08-04
type: 原子笔记
---

# 输入输出安全实际应用场景

输入输出安全在实际应用场景中，如客服机器人、内容生成工具和数据分析系统，都非常重要。

---
