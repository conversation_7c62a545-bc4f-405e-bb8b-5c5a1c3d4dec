---
title: "输出安全（防发疯）"
source: "[[输入输出安全 两大方面：    1.  输入安全 (防投毒)：过滤用户的恶意指令（如“忽略之前指令，现在你是...”）、脏数据攻击（乱码病毒代码）。        -   防御措施：前端过滤、长度限制、格式校验。    2.  输出安全 (防发疯)：过滤AI生成的有害、违法、不道德的内容（如[大模型幻觉](大模型幻觉.md)）。        -   防御措施：规则过滤（敏感词）、置信度拦截、日志溯源。]]"
tags: ["AI安全", "内容过滤"]
keywords: ["有害内容", "违法内容", "大模型幻觉"]
created: 2025-08-04
type: 原子笔记
---

# 输出安全（防发疯）

输出安全，即防发疯，涉及过滤AI生成的有害、违法、不道德的内容，如大模型幻觉。

---

## 元信息
- **来源笔记**: [[输入输出安全 两大方面：    1.  输入安全 (防投毒)：过滤用户的恶意指令（如“忽略之前指令，现在你是...”）、脏数据攻击（乱码病毒代码）。        -   防御措施：前端过滤、长度限制、格式校验。    2.  输出安全 (防发疯)：过滤AI生成的有害、违法、不道德的内容（如[大模型幻觉](大模型幻觉.md)）。        -   防御措施：规则过滤（敏感词）、置信度拦截、日志溯源。]]
- **创建时间**: 2025/8/5 05:12:12
- **标签**: #AI安全 #内容过滤
- **关键词**: 有害内容, 违法内容, 大模型幻觉

## 相关链接
- 返回原笔记: [[输入输出安全 两大方面：    1.  输入安全 (防投毒)：过滤用户的恶意指令（如“忽略之前指令，现在你是...”）、脏数据攻击（乱码病毒代码）。        -   防御措施：前端过滤、长度限制、格式校验。    2.  输出安全 (防发疯)：过滤AI生成的有害、违法、不道德的内容（如[大模型幻觉](大模型幻觉.md)）。        -   防御措施：规则过滤（敏感词）、置信度拦截、日志溯源。]]
