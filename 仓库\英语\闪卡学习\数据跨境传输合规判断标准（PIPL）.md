---
关系: 用户随时能删数据（DSAR）→ 企业传数据出国前，必须三选一（安全评估/签合同/做认证）→ 违规就罚到肉疼    - 如果数据量小 → 签中国版标准合同（最快）；    - 如果数据含敏感信息（如身份证号）→ 走安全评估（最严）；    - 如果长期传数据 → 做个人信息保护认证（一劳永逸）。
决策树: true
人话: DSAR = 用户行使“删除权”的入口（比如网站上的“申请删除个人信息”按钮）。
已学: true
🍎重要: true
---
# 1	数据跨境传输合规判断标准（PIPL）

```mermaid
flowchart TD
    A[开始] --> B{数据类型是什么?}
    B -->|敏感个人信息\n（身份证/医疗/金融等）| C[必须安全评估]
    B -->|普通个人信息\n（姓名/电话/地址）| D{出境人数≥100万?}
    D -->|是| C
    D -->|否| E{涉及重要数据?\n（宏观经济/供应链等）}
    E -->|是| C
    E -->|否| F[仅需标准合同]
    C --> G[完成]
    F --> G
```







### 1.1.1	**1. 用户要求删数据（DSAR）是什么意思？**

- **原短语**：`data_subject_rights_portal (DSAR)`
    
- **人话**：  
    **DSAR = 用户行使“删除权”的入口**（比如网站上的“申请删除个人信息”按钮）。
    
    - 例子：你在某APP上看到自己的历史订单，想永久删除，就通过DSAR提交申请，企业必须在规定时间内处理。
        

---

### 1.1.2	**2. 中国数据出境的三条合法路径（PIPL）**

|原短语|人话解释|适用场景|
|---|---|---|
|**1. 安全评估（政府审批）**|重要数据出境前，必须向网信办申请批准。  <br>（耗时最长，成本最高）|适合：  <br>- 涉及国家安全的数据  <br>- 超100万人个人信息出境|
|**2. 签中国版标准合同**|和境外接收方签中国官方发布的“数据出境合同模板”。  <br>（类似欧盟的SCCs，但条款更严格）|适合：  <br>- 普通个人信息出境  <br>- 中小企业常用|
|**3. 个人信息保护认证**|通过第三方机构认证（类似ISO认证），证明你保护数据的能力达标。|适合：  <br>- 频繁但低风险的数据出境  <br>- 电商、跨境物流等|

---

### 1.1.3	**3. 完整链条：从用户权利到跨境传输**

**场景**：一家中国跨境电商，要把用户订单数据（含姓名、地址）传到美国服务器分析。

#### *******	**步骤1：用户控制数据（DSAR）**

- 用户发现数据被滥用，通过DSAR门户要求删除 → 企业必须删除并停止跨境传输该数据。
    

#### *******	**步骤2：企业选择出境合规路径（PIPL三选一）**

- 如果数据量小 → 签**中国版标准合同**（最快）；
    
- 如果数据含敏感信息（如身份证号）→ 走**安全评估**（最严）；
    
- 如果长期传数据 → 做**个人信息保护认证**（一劳永逸）。
    

#### *******	**步骤3：落地执行**

- 签完合同或通过评估后，数据才能传出国。
    
- 境外服务器收到数据后，仍需按合同保护（如加密存储）。
    

#### *******	**步骤4：违规后果**

- 如果没走上述任何一条路 → 按PIPL罚款**最高5000万或年营收5%**，还可能被停业。
    

---

### 1.1.4	**4. 和欧盟（GDPR）的对比**

|环节|中国（PIPL）|欧盟（GDPR）|
|---|---|---|
|**用户权利**|DSAR删除权（类似GDPR）|完全一致|
|**出境工具**|安全评估/中国合同/认证|SCCs/BCR/充分性认定|
|**惩罚力度**|最高5000万或5%营收|最高2000万欧元或4%全球营收|

---

### 1.1.5	**一句话总结整条链**

**用户随时能删数据（DSAR）→ 企业传数据出国前，必须三选一（安全评估/签合同/做认证）→ 违规就罚到肉疼。**

如果需要更具体的例子（比如某家公司实际怎么操作的），可以告诉我！


### 1.1.6	**1. 数据跨境传输（核心问题）**

|原短语|人话解释|
|---|---|
|**cross_border_transfers**|把数据从一个国家传到另一个国家（比如中国公司用美国服务器）。|
|**SCCs_Standard_Contractual_Clauses**|欧盟给的“标准合同模板”，签了才能合法传数据到境外。|
|**Binding_Corporate_Rules (BCRs)**|跨国公司自己定的内部数据保护规则，申请批准后，集团内传数据不用反复签合同。|
|**adequacy_decisions**|欧盟认证某些国家（如日本）数据保护水平高，传数据过去免签合同。|
|**PIPL_Article_38**|中国《个人信息保护法》第38条：数据出境必须通过安全评估或签中国版合同。|

---

### 1.1.7	**2. 角色与责任**

|原短语|人话解释|
|---|---|
|**data_controller_and_processor**|- **控制者**（Controller）：决定为啥处理数据（比如公司决定收集用户地址）。  <br>- **处理者**（Processor）：替控制者干活（比如外包客服公司）。|
|**processor-to-subprocessor_transfers**|处理者把数据转包给第三方（比如云服务商转给印度外包团队），也得签合同！|

---

### 1.1.8	**3. 合规工具与操作**

|原短语|人话解释|
|---|---|
|**data_processing_agreement**|控制者和处理者之间的协议，明确“数据怎么用、怎么保护”。|
|**data_subject_rights_portal (DSAR)**|用户提交请求的入口（比如要求删除数据），企业必须及时处理。|
|**privacy_by_design_default**|产品设计时默认保护隐私（比如APP默认不收集位置）。|

---

### 1.1.9	**4. 中国特别规则**

|原短语|人话解释|
|---|---|
|**中国PIPL跨境传输合规体系**|中国数据出境的三种合法方式：  <br>1. 通过安全评估（政府审批）；  <br>2. 签中国版标准合同；  <br>3. 做个人信息保护认证。|

---

### 1.1.10	**5. 英语表达类（图谱中的“错题本”）**

|原短语|人话解释|
|---|---|
|**passive_to_proactive_transformation**|把被动语态改成主动（比如不说“Mistakes were made”，而说“We made mistakes”）。|
|**counterargument_refutation_technique**|辩论中反驳对方的套路（比如“您说的对，但实际情况是…”）。|

---

### 1.1.11	**逻辑关系总结**

1. **跨境传数据？**
    
    - 欧盟：签SCCs或用BCR（大公司专属）。
        
    - 中国：走安全评估或签中国版合同。
        
2. **谁负责？**
    
    - 控制者（老板）和处理者（打工的）都得合规。
        
3. **用户权利**：
    
    - 用户能要求删数据（DSAR门户提交请求）。
        
4. **避免罚款**：
    
    - 产品设计默认保护隐私（privacy by design）。
        

**一句话**：

- **数据出国要“签证”**：欧盟认SCCs/BCR，中国认安全评估。
    
- **谁传谁负责**，用户随时能维权。
    
- 英语表达要专业（尤其写合同或辩论时）。### **1. 核心问题：数据跨境传输怎么合法？**

- **场景**：比如一家中国公司把用户数据传到美国服务器，或欧盟公司用印度的客服系统。
    
- **关键限制**：
    
    - **GDPR**：数据传出欧盟，必须合法。
        
    - **PIPL**：数据传出中国，必须合法。
        

---

### 1.1.12	**2. 解决方案（怎么传才不违法？）**

#### ********	**（1）欧盟（GDPR）的合法途径**

- **SCCs（标准合同条款）**
    
    - 是什么：欧盟提供的“合同模板”，双方签字承诺保护数据。
        
    - 怎么用：比如美国公司要和德国公司传数据，签这份合同就行。
        
    - 节点关联：
        
        - `SCCs_under_GDPR_Article_46` → `lawful_data_transfers`
            
        - `processor-to-subprocessor_transfers`（子处理者也要签）。
            
- **BCRs（绑定企业规则）**
    
    - 是什么：跨国公司内部自定的数据保护规则（适合大集团）。
        
- **充分性认定（adequacy_decisions）**
    
    - 是什么：欧盟认可某些国家（如日本）的数据保护水平，传这些国家免签SCCs。
        

#### ********	**（2）中国（PIPL）的合法途径**

- **中国版SCCs**：类似欧盟，但需符合中国监管要求（节点`中国PIPL跨境传输合规体系`）。
    
- **安全评估**：重要数据出境需通过政府审批。
    

---

### 1.1.13	**3. 关键概念关系**

- **跨境传输（cross_border_transfers）**是树干，**SCCs/BCRs**是树枝。
    
    - 没有SCCs或BCRs → 传输违法（GDPR罚款可达全球营收4%）。
        
- **GDPR和PIPL的差异**：
    
    - 欧盟：SCCs是主流（`can_be_achieved_through_SCCs`）。
        
    - 中国：更依赖安全评估（`PIPL_Article_38_strict_requirements`）。
        

---

### 1.1.14	**4. 其他节点解释**

- **data_controller_and_processor**：
    
    - 控制者（决定数据处理目的）和处理者（代工处理数据）都要对传输负责。
        
- **英语错题本/辩论术语**：
    
    - 这部分是教你如何用专业英语解释合规逻辑（比如演讲或写合同）。例如：
        
        - 错误：“We can transfer data freely.”
            
        - 正确：“Transfers require SCCs under Article 46.”
            

---

### 1.1.15	**一句话总结**

- **欧盟传数据**：签SCCs合同 → 合法。
    
- **中国传数据**：走安全评估或中国版SCCs → 合法。
    
- **企业要做的事**：
    
    1. 搞清楚传哪去；
        
    2. 选对工具（SCCs/BCRs/评估）；
        
    3. 写进合同（别废话，按模板来）。
        

**说白了就是：数据出国要“签证”，SCCs是通行证。**