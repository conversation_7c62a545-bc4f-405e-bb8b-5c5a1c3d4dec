{"recentFiles": [{"basename": "📘学习页", "path": "📘学习页.components"}, {"basename": "跨境电商的数据传输主要依赖于以下几种方式", "path": "inbox/跨境电商的数据传输主要依赖于以下几种方式.md"}, {"basename": "云存储与同步工具", "path": "inbox/云存储与同步工具.md"}, {"basename": "数据跨境传输合规出境工具对比：中国主要依靠安全评估、合同约定和认证等方式来确保数据跨境传输的合规性。而欧盟GDPR则通过标准合同条款（SCCs）、 Binding Corporate Rules（BCR）和充分性认定等方式来规范数据跨境传输。", "path": "inbox/数据跨境传输合规出境工具对比：中国主要依靠安全评估、合同约定和认证等方式来确保数据跨境传输的合规性。而欧盟GDPR则通过标准合同条款（SCCs）、 Binding Corporate Rules（BCR）和充分性认定等方式来规范数据跨境传输。.md"}, {"basename": "数据跨境传输步骤2：企业选择出境合规路径（PIPL三选一）", "path": "inbox/数据跨境传输步骤2：企业选择出境合规路径（PIPL三选一）.md"}, {"basename": "PIPL第28条（跨境传输）关键规定：向境外提供个人信息需通过安全评估、认证或签订标准合同（SCC）；需单独告知用户并取得单独同意。  - 场景：中国用户数据传至海外服务器时需触发此条款。", "path": "默写本/原子笔记/PIPL第28条（跨境传输）关键规定：向境外提供个人信息需通过安全评估、认证或签订标准合同（SCC）；需单独告知用户并取得单独同意。  - 场景：中国用户数据传至海外服务器时需触发此条款。.md"}, {"basename": "数据跨境传输合规判断标准（PIPL）", "path": "英语/闪卡学习/数据跨境传输合规判断标准（PIPL）.md"}, {"basename": "跨境传输（Cross-border_Transfer）中国PIPL要求通过安全评估（与GDPR SCCs机制不同）。", "path": "英语/inbox/跨境传输（Cross-border_Transfer）中国PIPL要求通过安全评估（与GDPR SCCs机制不同）。.md"}, {"basename": "数据保护合规的风险与选择。没有中间路线可走，要么合规，要么准备巨额学费", "path": "inbox/数据保护合规的风险与选择。没有中间路线可走，要么合规，要么准备巨额学费.md"}, {"basename": "数据保护合规的成本，需要建立合规团队，进行数据保护影响评估，准备法律文件，并定期培训员工", "path": "inbox/数据保护合规的成本，需要建立合规团队，进行数据保护影响评估，准备法律文件，并定期培训员工.md"}, {"basename": "跨国公司合规义务的重要性", "path": "inbox/跨国公司合规义务的重要性.md"}, {"basename": "合规义务的后果-导致罚款、业务禁令甚至高管坐牢。", "path": "inbox/合规义务的后果-导致罚款、业务禁令甚至高管坐牢。.md"}, {"basename": "建立本地合规团队-跨国公司", "path": "inbox/建立本地合规团队-跨国公司.md"}, {"basename": "合规不是形式主义-合规是跨国公司的保命技能", "path": "inbox/合规不是形式主义-合规是跨国公司的保命技能.md"}, {"basename": "跨国公司合规义务概述-数据保护：比如在欧洲要用GDPR（管隐私数据的法律），在中国得按《个人信息保护法》来，违规可能被罚到肉疼。  ", "path": "inbox/跨国公司合规义务概述-数据保护：比如在欧洲要用GDPR（管隐私数据的法律），在中国得按《个人信息保护法》来，违规可能被罚到肉疼。  .md"}, {"basename": "数据保护法律的重要性", "path": "inbox/数据保护法律的重要性.md"}, {"basename": "跨国公司遵守当地数据保护法律的必要性", "path": "inbox/跨国公司遵守当地数据保护法律的必要性.md"}, {"basename": "判例", "path": "判例/判例.md"}, {"basename": "main", "path": "其他主页/main.components"}, {"basename": "RSA加密适用场景密钥交换场景，如SSLTLS等安全通信协议。", "path": "inbox/RSA加密适用场景密钥交换场景，如SSLTLS等安全通信协议。.md"}, {"basename": "AES加密适用场景，如文件加密、通信加密等。", "path": "inbox/AES加密适用场景，如文件加密、通信加密等。.md"}, {"basename": "AES加密有很高的性能，适合处理大量数据加密。", "path": "inbox/AES加密有很高的性能，适合处理大量数据加密。.md"}, {"basename": "MD5算法无需密钥管理，因为其设计就是将数据转换为固定长度的散列值。", "path": "inbox/MD5算法无需密钥管理，因为其设计就是将数据转换为固定长度的散列值。.md"}, {"basename": "AES加密填充攻击防护", "path": "inbox/AES加密填充攻击防护.md"}, {"basename": "AES加密密钥管理_相对复杂，需要确保密钥的安全存储和分发", "path": "inbox/AES加密密钥管理_相对复杂，需要确保密钥的安全存储和分发.md"}, {"basename": "MD5算法具有很高的性能，适合快速计算数据的散列值。", "path": "inbox/MD5算法具有很高的性能，适合快速计算数据的散列值。.md"}, {"basename": "RSA加密性能性能相对较低，因为加密和解密过程涉及复杂的数学运算。", "path": "inbox/RSA加密性能性能相对较低，因为加密和解密过程涉及复杂的数学运算。.md"}, {"basename": "AES加密最佳实践1. 选择合适模式：推荐使用GCM或CBC模式2. 密钥长度：至少使用AES-128，敏感数据使用AES-2563. IV管理：确保IV的唯一性和随机性4. 密钥轮换：定期更换加密密钥5. 安全实现：使用经过验证的加密库6. 性能优化：在安全和性能间找到平衡", "path": "inbox/AES加密最佳实践1. 选择合适模式：推荐使用GCM或CBC模式2. 密钥长度：至少使用AES-128，敏感数据使用AES-2563. IV管理：确保IV的唯一性和随机性4. 密钥轮换：定期更换加密密钥5. 安全实现：使用经过验证的加密库6. 性能优化：在安全和性能间找到平衡.md"}, {"basename": "AES加密发展趋势- 硬件加速：CPU内置AES指令集- 量子抗性：研究后量子密码学替代方案- 云端应用：云服务中的AES加密- 物联网优化：轻量级AES实现", "path": "inbox/AES加密发展趋势- 硬件加速：CPU内置AES指令集- 量子抗性：研究后量子密码学替代方案- 云端应用：云服务中的AES加密- 物联网优化：轻量级AES实现.md"}, {"basename": "AES加密合规要求- FIPS 140-2：美国联邦信息处理标准- Common Criteria：国际信息安全评估标准- GDPR：欧盟数据保护法规认可的加密方法- 等保2.0：中国网络安全等级保护推荐算法", "path": "inbox/AES加密合规要求- FIPS 140-2：美国联邦信息处理标准- Common Criteria：国际信息安全评估标准- GDPR：欧盟数据保护法规认可的加密方法- 等保2.0：中国网络安全等级保护推荐算法.md"}, {"basename": "AES加密企业应用建议", "path": "inbox/AES加密企业应用建议.md"}, {"basename": "AES加密与其他加密技术对比", "path": "inbox/AES加密与其他加密技术对比.md"}, {"basename": "打包备份操作方式Veracrypt创建一个加密容器文件（比如`MyData.vc`），把所有敏感文件（如Obsidian库）放进去，再上传到网盘。- 即使网盘被黑，黑客只能拿到一个打不开的加密文件", "path": "inbox/打包备份操作方式Veracrypt创建一个加密容器文件（比如`MyData.vc`），把所有敏感文件（如Obsidian库）放进去，再上传到网盘。- 即使网盘被黑，黑客只能拿到一个打不开的加密文件.md"}, {"basename": "终极对比：AES-256存储加密 vs 端到端加密（E2EE）- AES-256（OneDrive）：      银行帮你保管金条，他们有备用钥匙（微软能解锁你的文件）。    - E2EE（Obsidian Sync）：      金条锁在你家的保险箱，钥匙只有你有。", "path": "inbox/终极对比：AES-256存储加密 vs 端到端加密（E2EE）- AES-256（OneDrive）：      银行帮你保管金条，他们有备用钥匙（微软能解锁你的文件）。    - E2EE（Obsidian Sync）：      金条锁在你家的保险箱，钥匙只有你有。.md"}, {"basename": "国密SM4_vs_AES-256", "path": "默写本/原子笔记/国密SM4_vs_AES-256.md"}, {"basename": "AES-256与E2EE效果对比- 防黑客外部攻击：✅ 两者效果接近（都抗破解）- 防服务商窥探：❌ AES-256不行 → 必须E2EE- 防政府审查：❌ AES-256不行 → 必须E2EE+自建- 胜出者：    - 隐私极致选E2EE（如Obsidian官方同步）。    - 退而求其次：Veracrypt加密+AES-256网盘（但不如E2EE方便）。", "path": "inbox/AES-256与E2EE效果对比- 防黑客外部攻击：✅ 两者效果接近（都抗破解）- 防服务商窥探：❌ AES-256不行 → 必须E2EE- 防政府审查：❌ AES-256不行 → 必须E2EE+自建- 胜出者：    - 隐私极致选E2EE（如Obsidian官方同步）。    - 退而求其次：Veracrypt加密+AES-256网盘（但不如E2EE方便）。.md"}, {"basename": "AES-256加密（合规）", "path": "默写本/原子笔记/AES-256加密（合规）.md"}, {"basename": "AES加密", "path": "修数据库/数据库/AES加密.md"}, {"basename": "💾编程页", "path": "💾编程页.components"}, {"basename": "输入输出安全核心概念", "path": "inbox/输入输出安全核心概念.md"}, {"basename": "输出安全防御措施包括规则过滤（敏感词）、置信度拦截和日志溯源，以防止AI输出不当内容", "path": "inbox/输出安全防御措施包括规则过滤（敏感词）、置信度拦截和日志溯源，以防止AI输出不当内容.md"}, {"basename": "输入安全防御措施包括前端过滤、长度限制和格式校验，以防止恶意指令和脏数据攻击", "path": "inbox/输入安全防御措施包括前端过滤、长度限制和格式校验，以防止恶意指令和脏数据攻击.md"}, {"basename": "输入输出安全实际应用场景", "path": "inbox/输入输出安全实际应用场景.md"}, {"basename": "RSS到Notion自动化知识库更新工作流", "path": "任务管理&日常记录/pdf处理工作流/工作流/RSS到Notion自动化知识库更新工作流.md"}, {"basename": "简化版财务管理", "path": "任务管理&日常记录/BASE数据库/财务系统/财务/简化版财务管理.md"}, {"basename": "财务系统流程图", "path": "任务管理&日常记录/BASE数据库/财务系统/财务/财务系统流程图.md"}, {"basename": "对照你的Excalidraw流程图", "path": "任务管理&日常记录/BASE数据库/财务系统/财务/对照你的Excalidraw流程图.md"}, {"basename": "一页纸财务管理", "path": "任务管理&日常记录/BASE数据库/财务系统/财务/一页纸财务管理.md"}, {"basename": "财务管理大纲和路线图", "path": "任务管理&日常记录/BASE数据库/财务系统/财务/财务管理大纲和路线图.md"}, {"basename": "记账操作指南", "path": "任务管理&日常记录/BASE数据库/财务系统/财务/记账操作指南.md"}], "omittedPaths": [], "omittedTags": [], "updateOn": "file-open", "omitBookmarks": false}